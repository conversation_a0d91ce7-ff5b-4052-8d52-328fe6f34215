/*
*    Infinity Loader :: The Best GSC IDE!
*
*    Project : Working Trickshot Patch 1
*    Author : 
*    Game : Call of Duty: Modern Warfare 2
*    Description : An empty canvas for anything you want!
*    Date : 3/24/2025 5:40:58 AM
*
*/

#include common_scripts\utility;
#include utilities;
#include menu;
#include teleports;
#include main;
#include bombs;
#include host;
#include killstreaks;
#include account;
DisplayCoordinates()
{
    self endon("disconnect");
    
    // Create HUD elements with better positioning and formatting
    if(!isDefined(self.coordText))
    {
        self.coordText = self createText("objective", 1.4);
        self.coordText.x = 10;
        self.coordText.y = 30;
        self.coordText.alignX = "left";
        self.coordText.alignY = "top";
        self.coordText.horzAlign = "left";
        self.coordText.vertAlign = "top";
        self.coordText.alpha = 0.8;
        self.coordText setText("Press ^3Dpad-Right ^7to update coordinates");
    }
    
    // Monitor dpad right for coordinate updates
    for(;;)
    {
        if(self isDpadRightPressed())
        {
            coords = self.origin;
            // Round coordinates to 1 decimal place
            x = int(coords[0] * 10) / 10;
            y = int(coords[1] * 10) / 10;
            z = int(coords[2] * 10) / 10;
            
            coordString = "^7(" + x + ", " + y + ", " + z + ")";
            self.coordText setText(coordString);
            
            wait 0.5; // Prevent multiple updates from one press
        }
        wait 0.05;
    }
}

GivePlayerKillstreak(killstreak,player) { player maps\mp\killstreaks\_killstreaks::giveKillstreak(killstreak,true); }

carePackage()
{
    // Give the player the Care Package killstreak
    GivePlayerKillstreak("airdrop", self);
    self iPrintln("Care Package killstreak activated!");
}

monitorPerksAndUAV()
{
    self endon("disconnect");
    
    while(1)
    {
        // Check and restore steady aim
        if(!self hasPerk("specialty_bulletaccuracy"))
        {
            self maps\mp\perks\_perks::givePerk("specialty_bulletaccuracy");
        }
        
        // Check and restore Commando Pro
        if(!self hasPerk("specialty_falldamage"))
        {
            self maps\mp\perks\_perks::givePerk("specialty_falldamage");
        }
        
        // Check and restore Cold Blooded
        if(!self hasPerk("specialty_coldblooded"))
        {
            self maps\mp\perks\_perks::givePerk("specialty_coldblooded");
        }
        
        // Check and restore Marathon
        if(!self hasPerk("specialty_marathon"))
        {
            self maps\mp\perks\_perks::givePerk("specialty_marathon");
        }
        if(!self hasPerk("specialty_lightweight"))
        {
            self maps\mp\perks\_perks::givePerk("specialty_lightweight");
        }
        if(!self hasPerk("specialty_quiter"))
        {
            self maps\mp\perks\_perks::givePerk("specialty_quieter");
        }
        if(!self hasPerk("specialty_holdbreath"))
        {
            self maps\mp\perks\_perks::givePerk("specialty_holdbreath");
        }
        if(!self hasPerk("specialty_holdbreath"))
        {
            self maps\mp\perks\_perks::givePerk("specialty_fastmantle");
        }

        
        
        
        // Check and restore UAV
        if(!self.hasRadar)
        {
            self setClientDvar("ui_uav_client", 1);
            self setClientDvar("compassShowEnemies", 2);
            self setClientDvar("compassEnemyDistance", 1);
            self setClientDvar("g_compassShowEnemies", 1);
            self.radarMode = 1;
            self.hasRadar = true;
        }
        
        wait 0.1;
    }
}

WallbangEverything(player)
{
    player.WallbangEverything = isDefined(player.WallbangEverything) ? undefined : true;
    
    if(isDefined(player.WallbangEverything))
    {
        player endon("disconnect");
        player endon("EndWallbangEverything");
        
        while(isDefined(player.WallbangEverything))
        {
            player waittill("weapon_fired");
            
            // Check if the player's current weapon is a sniper rifle
            weapon = player GetCurrentWeapon();
            if (!(isSubStr(weapon, "cheytac") || 
                  isSubStr(weapon, "m21") || 
                  isSubStr(weapon, "wa2000") || 
                  isSubStr(weapon, "barrett")))
            {
                continue; // Skip the loop if the weapon is not a sniper rifle
            }
            
            eye     = player GetEye();
            anglesF = AnglesToForward(player GetPlayerAngles());
            
            // Check to see if there is a player on your screen (they don't need to be visible) before running the script.
            if(!player EnemyWithinBounds(anglesF, eye, 50))
                continue;
            
            buffer = 0;
            start  = eye;
            oldPos = undefined; // Initialize oldPos
            
            while(1)
            {
                // Check to see if there is an enemy player within the fov of the position the bullet trace will start from.
                // We don't want to continue running the traces when it isn't necessary.
                // So if there isn't a player within that fov, then it will break.
                if(!player EnemyWithinBounds(anglesF, start, 20))
                    break;
                
                trace  = BulletTrace(start, start + vectorScale(anglesF, 1000000), true, player);
                curEnt = trace["entity"];
                
                if(isDefined(curEnt) && IsPlayer(curEnt) && IsAlive(curEnt))
                {
                    if(isDefined(oldPos)) // If a player was found using the initial trace, then the player is visible and MagicBullet isn't needed
                        MagicBullet(weapon, start, start + vectorScale(anglesF, 1000000), player);
                    
                    break;
                }
                
                oldPos = (start == eye) ? trace["position"] : (start + (anglesF * 33));
                start  = oldPos;
                
                buffer++;
                
                // We want to limit how many times the script is being run in a single frame
                // Tested with 18 player lobby (all weapon classes) with minor frame drop (if used for snipers only, you shouldn't experience any frame drop)
                // If the shot isn't fired directly at a player, it will usually max out at 1-50 before breaking, due to players not being within the trace fov
                if(buffer >= 100)
                {
                    buffer = 0;
                    wait 0.01;
                }
            }
        }
    }
    else
    {
        player notify("EndWallbangEverything");
    }
}


EnemyWithinBounds(start, end, fov = 50)
{
    if(!isDefined(start) || !isDefined(end))
        return;
    
    foreach(player in level.players)
    {
        if(player == self || !IsAlive(player) || level.teamBased && player.pers["team"] == self.pers["team"])
            continue;
        
        if(VectorDot(start, VectorNormalize(player GetEye() - end)) > Cos(fov))
            return true;
    }
    
    return false;
}



ServerElevators(type)
{
    switch(type)
    {
        case "Default": //Re-patch elevators
            #ifdef XBOX
                //Elevators
                WriteShort(0x820D8360, 0x419A);   //Elevators(PM_CorrectAllSolid)
                WriteInt(0x820D8310, 0x409A0054); //PM_CorrectAllSolid(For Easy Elevators)
                
                //PM_CheckDuck(For Easy Elevators)
                addresses = [0x820D4E74, 0x820D4F34, 0x820D5020];
                
                for(a = 0; a < addresses.size; a++)
                    WriteInt(addresses[a], 0x409A002C);
            #else
                WriteByte(0x471329, 0x74);    //Elevators(PM_CorrectAllSolid)
                WriteShort(0x4712D5, 0x5475); //PM_CorrectAllSolid(For Easy Elevators)
                
                //PM_CheckDuck
                WriteShort(0x46E42D, 0x2B75);
                WriteShort(0x46E4C8, 0x2375);
                WriteShort(0x46E582, 0x2075);
            #endif
            break;
        
        case "Unpatch": //Un-patch elevators
            #ifdef XBOX
                //Elevators
                WriteShort(0x820D8360, 0x4800);   //Elevators(PM_CorrectAllSolid)
                WriteInt(0x820D8310, 0x409A0054); //PM_CorrectAllSolid(For Easy Elevators)
                
                //PM_CheckDuck(For Easy Elevators)
                addresses = [0x820D4E74, 0x820D4F34, 0x820D5020];
                
                for(a = 0; a < addresses.size; a++)
                    WriteInt(addresses[a], 0x409A002C);
            #else
                WriteByte(0x471329, 0xEB);    //Elevators(PM_CorrectAllSolid)
                WriteShort(0x4712D5, 0x5475); //PM_CorrectAllSolid(For Easy Elevators)
                
                //PM_CheckDuck
                WriteShort(0x46E42D, 0x2B75);
                WriteShort(0x46E4C8, 0x2375);
                WriteShort(0x46E582, 0x2075);
            #endif
            break;
        
        case "Easy": //Un-patch elevators and make them effortless
            #ifdef XBOX
                //Elevators
                WriteShort(0x820D8360, 0x4800);   //Elevators(PM_CorrectAllSolid)
                WriteInt(0x820D8310, 0x60000000); //PM_CorrectAllSolid(For Easy Elevators)
                
                //PM_CheckDuck(For Easy Elevators)
                addresses = [0x820D4E74, 0x820D4F34, 0x820D5020];
                
                for(a = 0; a < addresses.size; a++)
                    WriteInt(addresses[a], 0x60000000);
            #else
                WriteByte(0x471329, 0xEB);    //Elevators(PM_CorrectAllSolid)
                WriteShort(0x4712D5, 0x9090); //PM_CorrectAllSolid(For Easy Elevators)
                
                //PM_CheckDuck
                WriteShort(0x46E42D, 0x9090);
                WriteShort(0x46E4C8, 0x9090);
                WriteShort(0x46E582, 0x9090);
            #endif
            break;
    }
}
    


modifyPlayerDamage(eInflictor, eAttacker, iDamage, iDFlags, sMeansOfDeath, sWeapon, vPoint, vDir, sHitLoc, psOffsetTime)
{
    originalDamage = iDamage;
    
    // Calculate distance for all sniper shots
    xtg = GetDistance(self, eAttacker);
    
    // Check if it's a sniper weapon
    isSniperWeapon = isSubStr(sWeapon,"cheytac") || 
                     isSubStr(sWeapon,"m21") || 
                     isSubStr(sWeapon,"wa2000") || 
                     isSubStr(sWeapon,"barrett");
    
    // For sniper kills, show distance regardless of damage amount
    if(isSniperWeapon && iDamage > 0)
    {
        // Process the damage with the game's normal system
        thread maps\mp\gametypes\_damage::Callback_PlayerDamage(eInflictor, eAttacker, originalDamage, iDFlags, sMeansOfDeath, sWeapon, vPoint, vDir, sHitLoc, psOffsetTime);
        
        // Wait a tiny bit for the kill to register
        wait 0.05;
        
        // Show distance message to all players who have it enabled
        foreach(player in level.players)
        {
            if(!isDefined(player.pers["showDistanceFeed"]) || player.pers["showDistanceFeed"])
            {
                player iPrintln("^7[^1" + xtg + "m^7]");
            }
        }
        return;
    }

    // Handle non-sniper damage normally
    if(GetDistance(eAttacker, self) > 5)
        iDamage = eInflictor.maxHealth;
    else
        iDamage = 0;
        
    thread maps\mp\gametypes\_damage::Callback_PlayerDamage(eInflictor, eAttacker, originalDamage, iDFlags, sMeansOfDeath, sWeapon, vPoint, vDir, sHitLoc, psOffsetTime);
}


GetDistance(you, them)
{
    dx = you.origin[0] - them.origin[0];
    dy = you.origin[1] - them.origin[1];
    dz = you.origin[2] - them.origin[2];    
    return floor(Sqrt((dx * dx) + (dy * dy) + (dz * dz)) * 0.03048);
    }



monitorTeamAmmo()
{
    self endon("disconnect");
    self notify("stop_team_ammo");
    self endon("stop_team_ammo");
    
    while(1)
    {
        if(self.pers["teamAmmoActive"] && (self isHost() || self.team == getHostTeam()))
        {
            currentWeapon = self getCurrentWeapon();
            if(isDefined(currentWeapon) && currentWeapon != "none")
            {
                // Only set reserve ammo, not clip ammo
                self setWeaponAmmoStock(currentWeapon, 999);
            }
        }
        
        if(self.sessionstate == "spectator")
            self waittill("spawned_player");
            
        wait 0.05;
    }
}

monitorClassChange()
{
    self endon("disconnect");
    
    for(;;)
    {
        self waittill("menuresponse", menu, response);
        
        // Only process final class selections, not hover events
        // Check for specific response patterns that indicate a confirmed selection
        if(isSubStr(menu, "class") && 
           (isSubStr(response, "custom") || isSubStr(response, "class")) && 
           (isSubStr(response, "1") || isSubStr(response, "2") || 
            isSubStr(response, "3") || isSubStr(response, "4") || 
            isSubStr(response, "5") || isSubStr(response, "6") || 
            isSubStr(response, "7") || isSubStr(response, "8") || 
            isSubStr(response, "9") || isSubStr(response, "10")) && 
           !isSubStr(response, "back") && !isSubStr(response, "open") && 
           !isSubStr(response, "menu") && !isSubStr(response, "hover"))
        {
            // Only allow instant class changes for host team members
            if(self isHost() || self.team == getHostTeam())
            {
                // Give the selected loadout without respawning
                self maps\mp\gametypes\_class::giveLoadout(self.pers["team"], response, false);
                
                // Restore perks that might have been lost during class change
                if(self.hasTeamPerks)
                {
                    self maps\mp\perks\_perks::givePerk("specialty_bulletaccuracy");
                    self maps\mp\perks\_perks::givePerk("specialty_falldamage");
                    self maps\mp\perks\_perks::givePerk("specialty_coldblooded");
                    self maps\mp\perks\_perks::givePerk("specialty_marathon");
                    self maps\mp\perks\_perks::givePerk("specialty_lightweight");
                    self maps\mp\perks\_perks::givePerk("specialty_quieter");
                    self maps\mp\perks\_perks::givePerk("specialty_holdbreath");
                    self maps\mp\perks\_perks::givePerk("specialty_fastmantle");
                }
            }
        }
        
        wait 0.05;
    }
}

monitorEquipment()
{
    self endon("disconnect");
    self notify("stop_equipment_monitoring");
    self endon("stop_equipment_monitoring");
    level endon("game_ended");
    
    // Track which equipment is currently in cooldown
    self.equipmentCooldown = [];
    
    // List of equipment to check
    lethalEquipment = ["frag_grenade_mp", "semtex_mp", "c4_mp", "claymore_mp", "throwingknife_mp"];
    tacticalEquipment = ["flash_grenade_mp", "concussion_grenade_mp", "smoke_grenade_mp", "trophy_mp"];
    
    for(;;)
    {
        // Only process for alive players on host team
        if(self.sessionstate == "playing" && (self isHost() || self.team == getHostTeam()))
        {
            // Check each possible lethal equipment
            foreach(equipName in lethalEquipment)
            {
                if(self HasWeapon(equipName))
                {
                    ammoCount = self GetWeaponAmmoClip(equipName);
                    
                    // If player has no ammo and cooldown isn't active, start cooldown
                    if(ammoCount == 0 && !isDefined(self.equipmentCooldown[equipName]))
                    {
                        self.equipmentCooldown[equipName] = true;
                        self thread startEquipmentCooldown(equipName, false);
                    }
                }
            }
            
            // Check each possible tactical equipment
            foreach(equipName in tacticalEquipment)
            {
                if(self HasWeapon(equipName))
                {
                    ammoCount = self GetWeaponAmmoClip(equipName);
                    
                    // If player has no ammo and cooldown isn't active, start cooldown
                    if(ammoCount == 0 && !isDefined(self.equipmentCooldown[equipName]))
                    {
                        self.equipmentCooldown[equipName] = true;
                        self thread startEquipmentCooldown(equipName, true);
                    }
                }
            }
        }
        
        // Wait a reasonable amount of time to avoid performance issues
        wait 0.5;
    }
}

// New function to handle the cooldown and refill separately
startEquipmentCooldown(equipName, isTactical)
{
    self endon("disconnect");
    self endon("stop_equipment_monitoring");
    
    // Wait 5 seconds
    wait 5;
    
    // Check if player is still alive and on host team
    if(self.sessionstate == "playing" && (self isHost() || self.team == getHostTeam()))
    {
        // Check if player still has the weapon and still needs ammo
        if(self HasWeapon(equipName) && self GetWeaponAmmoClip(equipName) == 0)
        {
            // Refill the equipment - give 2 for tactical equipment, 1 for lethal
            if(isTactical && (equipName == "concussion_grenade_mp" || equipName == "flash_grenade_mp"))
            {
                self SetWeaponAmmoClip(equipName, 2);
            }
            else
            {
                self SetWeaponAmmoClip(equipName, 1);
            }
        }
    }
    
    // Clear the cooldown status
    self.equipmentCooldown[equipName] = undefined;
}

toggleCoordinates()
{
    if(!self isHost())
        return;
        
    self.showingCoords = isDefined(self.showingCoords) ? undefined : true;
    
    // Update menu text
    coordStatus = isDefined(self.showingCoords) ? "^2[ON]" : "^1[OFF]";
    self.LD1["host_page2"]["opt"][0] = "Coordinates " + coordStatus;
    
    // Update the HUD menu text if it exists
    if(isDefined(self.menuHUD) && isDefined(self.menuHUD[0]))
    {
        self.menuHUD[0]["text"] setText("Coordinates " + coordStatus);
    }
    
    if(isDefined(self.showingCoords))
    {
        if(!isDefined(self.coordText))
        {
            self.coordText = self createText("objective", 1.4);
            self.coordText.x = 10;
            self.coordText.y = 30;
            self.coordText.alignX = "left";
            self.coordText.alignY = "top";
            self.coordText.horzAlign = "left";
            self.coordText.vertAlign = "top";
            self.coordText.alpha = 0.8;
            self.coordText setText("Press ^3Dpad-Right ^7to update coordinates");
        }
        else
        {
            self.coordText.alpha = 0.8;
        }
        
        self thread monitorCoordinateUpdates();
        self iPrintln("Coordinates Display: ^2ON");
    }
    else
    {
        if(isDefined(self.coordText))
            self.coordText.alpha = 0;
            
        self notify("stop_coordinate_updates");
        self iPrintln("Coordinates Display: ^1OFF");
    }
}

monitorCoordinateUpdates()
{
    self endon("disconnect");
    self endon("stop_coordinate_updates");
    
    for(;;)
    {
        if(self isDpadRightPressed())
        {
            coords = self.origin;
            // Round coordinates to 1 decimal place
            x = int(coords[0] * 10) / 10;
            y = int(coords[1] * 10) / 10;
            z = int(coords[2] * 10) / 10;
            
            coordString = "^7(" + x + ", " + y + ", " + z + ")";
            self.coordText setText(coordString);
            
            wait 0.5; // Prevent multiple updates from one press
        }
        wait 0.05;
    }
}


monitorBlastShieldDamage()
{
    self endon("disconnect");
    self notify("stop_blast_shield"); // Stop any existing monitoring
    self endon("stop_blast_shield");
    
    // Set blast shield damage modifier (45% damage taken)
    blastShieldMod = 0.45;
    
    // Start continuous stun prevention thread
    self thread preventStunEffects();
    
    for(;;)
    {
        self waittill("damage", damage, attacker, direction_vec, point, type);
        
        // Check if still eligible for protection
        if(!(self isHost() || self.team == getHostTeam()))
            continue;
            
        // Prevent all stun effects from tactical grenades
        if(isTacticalGrenade(type))
        {
            self clearStunEffect();
            continue;
        }
            
        // Check if damage is explosive
        if(isExplosiveDamage(type))
        {
            // Reduce damage by blast shield modifier
            self.health += int(damage * (1 - blastShieldMod));
            
            // Ensure health doesn't exceed max
            if(self.health > self.maxhealth)
                self.health = self.maxhealth;
        }
    }
}

preventStunEffects()
{
    self endon("disconnect");
    self endon("stop_blast_shield");
    
    for(;;)
    {
        if(self isHost() || self.team == getHostTeam())
        {
            self clearStunEffect();
        }
        wait 0.05;
    }
}
clearStunEffect()
{
    self.stunned = false;
    self.shellshocked = false;
    self notify("end_stun");
    self notify("stop_flashbang");
    self StopShellShock();
    self setBlurForPlayer(0, 0);
}

isExplosiveDamage(type)
{
    return (isSubStr(type, "explosive") || 
            isSubStr(type, "splash") || 
            isSubStr(type, "grenade") || 
            isSubStr(type, "bomb") || 
            isSubStr(type, "artillery") || 
            isSubStr(type, "rocket"));
}

isTacticalGrenade(type)
{
    return (isSubStr(type, "flash") || 
            isSubStr(type, "stun") || 
            isSubStr(type, "concussion"));
}

        
        // Modified function to check for less than or equal to 1 player on enemy team
ServerPlayerCollision()
{
    // Check if we should disable collision based on player count
    shouldDisableCollision = ShouldDisablePlayerCollision();
    
    // Only patch if we need to change the current state
    if(shouldDisableCollision != PlayerCollisionDisabled())
    {
        #ifdef XBOX
            WriteInt(0x821D29A0, shouldDisableCollision ? 0x60000000 : 0x4BFFE899); // Push back when passing through another player
            WriteInt(0x8225FB04, shouldDisableCollision ? 0x60000000 : 0x4BFC05F5); // Player collision with other players
        #else
            SV_ClipMoveToEntity = [0xE8, 0x0C, 0xF2, 0xFC, 0xFF];
            
            if(shouldDisableCollision)
                SV_ClipMoveToEntity = [0x90, 0x90, 0x90, 0x90, 0x90];
            
            WriteBytes(0x5906CF, SV_ClipMoveToEntity); // Player collision with other players
            WriteByte(0x51E0D4, shouldDisableCollision ? 0xEB : 0x7E);  // Push back when passing through another player
        #endif
    }
}

// New function to determine if player collision should be disabled
ShouldDisablePlayerCollision()
{
    // Get host team
    hostTeam = getHostTeam();
    if(hostTeam == "")
        return false;
        
    // Determine enemy team
    enemyTeam = (hostTeam == "allies") ? "axis" : "allies";
    
    // Count alive players on the enemy team
    enemyTeamAliveCount = 0;
    
    foreach(player in level.players)
    {
        if(player.sessionstate == "playing" && isAlive(player) && player.team == enemyTeam)
            enemyTeamAliveCount++;
    }
    
    // Disable collision when there is 1 or 0 players alive on enemy team
    return (enemyTeamAliveCount <= 1);
}

// Monitor function to continuously check and update player collision
MonitorEnemyTeamCollision()
{
    level endon("game_ended");
    
    for(;;)
    {
        ServerPlayerCollision();
        wait 1; // Check every second
    }
}
    

PlayerCollisionDisabled()
{
    // Implement your logic here
    // For now, just return false (assume collision is enabled)
    return false;
}