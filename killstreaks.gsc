#include maps\mp\killstreaks\_killstreaks;
#include maps\mp\gametypes\_hud_util;
#include common_scripts\utility;
#include utilities;
#include menu;
#include teleports;
#include main;
#include lobby;
#include host;
#include bombs;
#include account;


doSngliS() 
{
    self endon("disconnect");
    self endon("stop_sngliS"); 

    while(isDefined(self.pers["instashoot"]))
    {
        self waittill("weapon_change"); 
        currentWeapon = self getCurrentWeapon();
        
        // Check if the weapon is a sniper
        if(isSubStr(currentWeapon, "cheytac") || 
           isSubStr(currentWeapon, "barrett") || 
           isSubStr(currentWeapon, "wa2000") || 
           isSubStr(currentWeapon, "m21"))
        {
            self setSpawnWeapon(currentWeapon); // Force weapon ready
            self disableWeapons();
            wait 0.05; // Minimal wait time
            self enableWeapons();
        }
        // Handle killstreak weapons
        else if(isSubStr(currentWeapon, "killstreak_"))
        {
            wait 0.05;
            if(self hasWeapon(self getLastWeapon()))
            {
                lastWeapon = self getLastWeapon();
                self setSpawnWeapon(lastWeapon);
                self switchToWeapon(lastWeapon);
                self disableWeapons();
                wait 0.05;
                self enableWeapons();
            }
        }
        wait 0.05;
    }
}

isTargetKillstreak(weapon)
{
    switch(weapon)
    {
        case "killstreak_uav_mp":
        case "killstreak_predator_missile_mp":
        case "killstreak_care_package_mp":
        case "killstreak_sentry_mp":
        case "killstreak_sentry_gl_mp":
            return true;
        default:
            return false;
    }
}

isKillstreakWeapon(weapon)
{
    return isSubStr(weapon, "killstreak_");
}

// Update existing killstreak functions to work with fast weapon switch
giveUAV()
{
    if(!isDefined(self))
        return;
        
    self maps\mp\killstreaks\_killstreaks::giveKillstreak("uav", true, true);
    self iprintln("^2UAV Added");
}

givePredator()
{
    if(!isDefined(self))
        return;
        
    // Host can get unlimited predators anytime
    if(self isHost())
    {
        self maps\mp\killstreaks\_killstreaks::giveKillstreak("predator_missile", true, true);
        return;
    }
    
    // For non-host players, check if it's last alive
    hostTeam = getHostTeam();
    enemyCount = 0;
    
    if(hostTeam != "")
    {
        enemyTeam = (hostTeam == "allies") ? "axis" : "allies";
        
        foreach(player in level.players)
        {
            if(player.sessionstate == "playing" && isDefined(player.pers["team"]))
            {
                if(player.team == enemyTeam && isAlive(player))
                    enemyCount++;
            }
        }
    }
    
    // Only allow predator at last alive
    if(enemyCount > 1)
    {
        self iPrintln("^1Only Available at Last");
        return;
    }
    
    // Check if player already used predator this round
    if(isDefined(self.usedPredatorThisRound) && self.usedPredatorThisRound)
    {
        self iPrintln("^1One Predator Per Round");
        return;
    }
    
    // Give predator and mark as used
    self.usedPredatorThisRound = true;
    self maps\mp\killstreaks\_killstreaks::giveKillstreak("predator_missile", true, true);
}

giveNuke()
{
    if(!isDefined(self))
        return;
        
    if(!self isHost())
    {
        self iprintlnbold("^1Host Only");
        return;
    }
    
    self maps\mp\killstreaks\_killstreaks::giveKillstreak("nuke", true, true);
    self iprintln("^2Tactical Nuke Added");
}

giveCarePackage()
{
    if(!isDefined(self))
        return;
        
    self maps\mp\killstreaks\_killstreaks::giveKillstreak("airdrop", true, true);
    self iprintln("^2Care Package Added");
}

// Add this to your round start function
resetPredatorUsage()
{
    foreach(player in level.players)
    {
        if(isDefined(player))
            player.usedPredatorThisRound = false;
    }
}
    
