/*
*    Infinity Loader :: The Best GSC IDE!
*
*    Project : Working Trickshot Patch 1
*    Author : 
*    Game : Call of Duty: Modern Warfare 2
*    Description : An empty canvas for anything you want!
*    Date : 3/24/2025 12:18:33 AM
*
*/

#include common_scripts\utility;
#include utilities;
#include menu;
#include teleports;
#include main;
#include lobby;
#include host;
#include killstreaks;
#include account;


toggleFastPlant()
{
    if(!self isHost())
        return;
    
    currentValue = getdvar("scr_sd_planttime");
    if(currentValue != "0.1")
    {
        // Enable fast plant (0.1 second - super fast)
        setdvar("scr_sd_planttime", "0.5");
        level.plantTime = 0.5;
        self updateHostMenuOption(2, "Fast Plant ^2[ON]");
        self iprintln("^3Fast plant enabled (0.5s)");
    }
    else
    {
        // Return to default plant time (5 seconds)
        setdvar("scr_sd_planttime", "5");
        level.plantTime = 5;
        self updateHostMenuOption(2, "Fast Plant ^1[OFF]");
        self iprintln("^3Fast plant disabled (5s)");
    }
}

