/*
*    Infinity Loader :: The Best GSC IDE!
*
*    Project : Working Trickshot Patch 1
*    Author : 
*    Game : Call of Duty: Modern Warfare 2
*    Description : An empty canvas for anything you want!
*    Date : 3/21/2025 1:44:54 AM
*
*/

#include common_scripts\utility;
#include main;
#include menu;
#include utilities;
#include bombs;
#include lobby;
#include host;
#include killstreaks;
#include account;


setupTeleports()
{
    // Add a safety check to prevent running this multiple times
    if(isDefined(level.teleportsInitialized))
        return;
    
    level.teleportsInitialized = true;
    
    // Initialize teleport activation variables
    level.teleportsEnabled = false;
    level.teleportActivationTime = 0;
    
    // Start the teleport activation timer
    thread monitorTeleportActivation();
    
    // Check which map is loaded and set up appropriate teleports
    mapname = getdvar("mapname");
    switch(mapname)
    {
        case "mp_checkpoint": // Karachi
            thread setupTeleportsKarachi();
            break;
            
        // Base Game Maps - Uncomment as implemented
        
        case "mp_afghan": // Afghan
            thread setupTeleportsAfghan();
            break;
        
        
        case "mp_derail": // Derail
            thread setupTeleportsDerail();
            break;
        
        /*
        case "mp_estate": // Estate
            thread setupTeleportsEstate();
            break;
        */
        
        case "mp_favela": // Favela
            thread setupTeleportsFavela();
            break;
        
        
        case "mp_highrise": // Highrise
            thread setupTeleportsHighrise();
            break;
        
        
        case "mp_invasion": // Invasion
            thread setupTeleportsInvasion();
            break;
        
        
        case "mp_quarry": // Quarry
            thread setupTeleportsQuarry();
            break;
        
        /*
        case "mp_rundown": // Rundown
            thread setupTeleportsRundown();
            break;
        */
        /*
        case "mp_rust": // Rust
            thread setupTeleportsRust();
            break;
        */
        
        case "mp_boneyard": // Scrapyard
            thread setupTeleportsScrapyard();
            break;
        
        
         case "mp_nightshift": // Skidrow
            thread setupTeleportsSkidrow();
            break;
        
        
        case "mp_subbase": // Sub Base
            thread setupTeleportsSubBase();
            break;
        

        case "mp_terminal": // Terminal
            thread setupTeleportsTerminal();
            break;
        
            
        case "mp_underpass": // Underpass
            thread setupTeleportsUnderpass();
            break;
            
        // Stimulus Package DLC Maps
        case "mp_complex": // Bailout
            thread setupTeleportsBailout();
            break;
            
            /*case "mp_crash": // Crash
            thread setupTeleportsCrash();
            break;
            
        case "mp_overgrown": // Overgrown
            thread setupTeleportsOvergrown();
            break;
            
        case "mp_compact": // Salvage
            thread setupTeleportsSalvage();
            break;
            
        case "mp_storm": // Storm
            thread setupTeleportsStorm();
            break;
            
        // Resurgence Package DLC Maps    
        case "mp_abandon": // Carnival
            thread setupTeleportsCarnival();
            break;
            
        case "mp_fuel2": // Fuel
            thread setupTeleportsFuel();
            break;
            
        case "mp_strike": // Strike
            thread setupTeleportsStrike();
            break;
            
        case "mp_trailerpark": // Trailer Park
            thread setupTeleportsTrailerPark();
            break;
            
            case "mp_vacant": // Vacant
            thread setupTeleportsVacant();
            break;*/
        }
}

setupTeleportsBailout()
{
    createOneWayTeleportFlag(
    (-2824.18, -2639.33, 680.125), 
    (-2686.84, -3002.09, 1436.79) // spawn to top
    );
    createOneWayTeleportFlag( // spawn to top other side
    (-2841.6, -2109.9, 680.1), 
    (-2647.9, -1607.6, 1460.6)
    );
    createOneWayTeleportFlag( // bounce side
    (2260.9, -1929.1, 592.1), 
    (2996.8, -982, 1238.3)
    );
    createOneWayTeleportFlag( // bounce out of map to bounce top
    (2657.9, -722.8, 396.1),
    (2996.8, -982, 1238.3)
    );
    createOneWayTeleportFlag( // to sui
    (652.9, -3687.1, 848.1), 
    (399, -4131, 975.3)
    );
    createOneWayTeleport(
    (-235.9, -1466.1, 672.1), 
    (275.2, -1781.4, 1232.2) // to middle building
    );
    createOneWayTeleport( // out of map to middle building
    (192, -862.5, 400.1), 
    (275.2, -1781.4, 1232.2)
    );
    createOneWayTeleportFlag(
    (-1560.6, -136.1, 384.1), 
    (-2647.9, -1607.6, 1460.6) // out of map to other side from spawn not the side with sui if that makes sense lol 
    );
    createOneWayTeleport( // on top of sui building from inside of sui building
    (660.9, -3523.3, 848.1), 
    (1683.6, -4041.3, 1279.7)
    );
    createOneWayTeleport(
    (-984.9, -3840, 848.1), // door with numbers lol the fucking door with numbers the numbers are 233
    (-1410.3, -3914, 1543)
    );
    createOnewayTeleport(
    (-504.9, -3720.8, 672.1), 
    (-1410.3, -3914, 1543) // front teleport lol lioke three doors far right]
    );
    createOneWayTeleport(
    (-192.7, -3720.9, 672.1),
    (-115.2, -4025.3, 1260.2)// front teleport lol lioke three doors middle one
    ); 
    createOneWayTeleport(
    (123.8, -3720.0, 672.1),
    (1101.2, -4226.2, 1134.3) // far left door to on top of sui building
    ); 
}
    
setupTeleportsAfghan()
    {
        createOneWayTeleport( // to top cliff
        (1987.18, 1911.32, -12.8143),
        (2018.74, 2838.25, 324.817)
        );
        createOneWayTeleportFlag( // to bounce sui
        (944.526, 854.309, 159.373),
        (-1052.17, 737.664, 224.735)
        );
        createOneWayTeleportFlag(
        (985.993, 1851.15, 406.796),
        (3738.81, -114.522, 367.125) // to far away side
        );
        createOneWayTeleportFlag(
        (2006.73, 4526.24, 260.043),
        (2876.93, 5155.38, 526.454)
        );
    
}
    
setupTeleportsDerail(){
    createOneWayTeleport(
    (-3165.49, -1729.77, 352.21),
    (-3442.91, -1814.4, 874.125) // house out of map
    );
    
    
    createTeleportPair(
    (-6591.48, 1801.29, 1847.27), // mountain
    (-6591.48, 1801.29, 1847.27)
    );
    
    createOneWayTeleportFlag(
    (-5046.94, 1665.73, 616.498), // mountain easy return
    (-6591.48, 1801.29, 1847.27) 
    );
}    
    
    
setupTeleportsFavela(){
    createOneWayTeleportFlag( // spawn corner to out of map spot
    (-1091.04, 2997.99, 280.616),
    (-1384.82, 2961.9, 704.115)
    );
    createOneWayTeleportFlag(
   (1046.86, 173.629, 310.108),
   (1789.23, -6.63702, 756.42) // out of map on bomb side
   );
   createOneWayTeleportFlag( // normal ele spot
   (-2016.98, 711.003, 8.125),
   (-1878.3, 523.299, 900.125)
   );
   createOneWayTeleportFlag( // top jambi
   (56.5917, 340.251, 312.125),
   (22.3332, 356.73, 448.125)
   );
   createOneWayTeleport(
   (230.916, 1079.38, 156.125),
   (500.826, 1656.54, 613.384) // to spawn building kinda
   );
   createOneWayTeleportFlag( // billboard
   (-1563.34, -443.778, 40.125),
   (-1854.19, -411.395, 672.125)
   );
   createOneWayTeleportFlag( // spawn
   (952.946, 2898.99, 292.836),
   (1173.75, 3138.11, 686.125)
   );
}

setupTeleportsSubBase()
    {
        createOneWayTeleportFlag(
        (568.981, -1496.95, 272.125),
        (1197.96, -4006.4, 456.036)
        );
        createOneWayTeleport(
        (975.05, 100.849, 48.125),
        (826.158, 190.21, 365.439)
        );
        createOneWayTeleportFlag(
        (-1584.87, -2536.88, 57.125),
        (-1846.83, -7065.8, 339.817)
        );
        
    
}
    

createTeleportPair(pos1, pos2, name1, name2) 
{
    // First teleport point
    trigger1 = spawn("trigger_radius", pos1, 0, 50, 50);
    if(!isDefined(trigger1))
        return;
    
    trigger1.targetPos = pos2;
    trigger1.name = name1;
    
    // Flag model for first point
    flagPos1 = pos1 + (0, 0, 10);
    model1 = spawn("script_model", flagPos1);
    if(isDefined(model1))
    {
        model1 setModel("prop_flag_neutral");
        model1.angles = (0, 0, 0);
        trigger1.flagModel = model1;
    }
    
    // Second teleport point
    trigger2 = spawn("trigger_radius", pos2, 0, 50, 50);
    if(!isDefined(trigger2))
        return;
    
    trigger2.targetPos = pos1;
    trigger2.name = name2;
    
    // Flag model for second point
    flagPos2 = pos2 + (0, 0, 10);
    model2 = spawn("script_model", flagPos2);
    if(isDefined(model2))
    {
        model2 setModel("prop_flag_neutral");
        model2.angles = (0, 0, 0);
        trigger2.flagModel = model2;
    }
    
    // Create minimap markers
    trigger1.objID = createMapMarker(pos1);
    trigger2.objID = createMapMarker(pos2);
    
    // Start monitoring
    trigger1 thread monitorTrigger();
    trigger2 thread monitorTrigger();
}

monitorTrigger()
{
    level endon("game_ended");
    
    while(true)
    {
        self waittill("trigger", player);
        
        // Only teleport actual players
        if(isDefined(player) && isPlayer(player))
        {
            // If player is host, they can teleport anytime
            if(player isHost())
            {
                // Check cooldown for teleport
                if(!isDefined(player.lastTeleportTime) || getTime() - player.lastTeleportTime > 2000)
                {
                    player thread teleportPlayer(self);
                }
            }
            // For non-host players on host team, only when 1 enemy remains
            else if(player.team == getHostTeam())
            {
                // Get current enemy count
                hostTeam = getHostTeam();
                enemyCount = 0;
                
                if(hostTeam != "")
                {
                    enemyTeam = (hostTeam == "allies") ? "axis" : "allies";
                    
                    foreach(otherPlayer in level.players)
                    {
                        if(otherPlayer.sessionstate == "playing" && isDefined(otherPlayer.pers["team"]))
                        {
                            if(otherPlayer.team == enemyTeam && isAlive(otherPlayer))
                                enemyCount++;
                        }
                    }
                }
                
                // Teleports are active when only 1 enemy remains
                if(enemyCount <= 1)
                {
                    // Check cooldown for teleport
                    if(!isDefined(player.lastTeleportTime) || getTime() - player.lastTeleportTime > 2000)
                    {
                        player thread teleportPlayer(self);
                    }
                }
                else
                {
                    // Only show message if we haven't shown one recently
                    if(!isDefined(player.lastTeleportMessageTime) || getTime() - player.lastTeleportMessageTime > 3000)
                    {
                        player iPrintLnBold("^1Teleports not Active");
                        player.lastTeleportMessageTime = getTime();
                    }
                }
            }
        }
        
        // Small wait to prevent multiple teleports
        wait 0.1;
    }
}

setupTeleportsSkidrow()
{
    // Add level endon to prevent issues if map changes
    
    createOneWayTeleport(
        (-1210.22, 156.985, 24.125),
        (-1734.59, 21.7933, 712.125) // above spawn bomb from door
    );
    
    createOneWayTeleport(
    (-1984.87, -1695.62, 0.125001), // barber to billboard
        (-2286.83, -1006.67, 1089.19)
    );
    
    createOneWayTeleport(
    (-1579.13, -2106.39, 24.125), 
    (-1347.07, -2473.17, 768.125) // by normal ele spot
        ); 
    createOneWayTeleport(
    (-1984.94, -3243.44, 152.125), 
    (-2176.42, -3296.95, 848.125)
    ); // bag art door
    createOneWayTeleport(
    (-1551.03, -3247.35, 152.062),
    (-1327.89, -3282.32, 720.125) // silvver line door
    );
    createOneWayTeleport(
    (-1803.59, -3961, 200.125),
    (-1794.97, -4183.89, 888.125) // all the way back by the first few teleoprts out the map
    );
    createOneWayTeleport(
    (15.016, -1082.22, 32.125),
    (-109.351, -1052.35, 656.125) // door by sniper spot outside
    );
    createOneWayTeleport(
    (1104.98, -1891.3, 32.125),
    (1051.57, -2125.64, 504.125)
    ); // spawn building with stairs]
    createOneWayTeleport(
    (937.441, 376.962, 16.125),
    (967.231, 696.04, 712.125)
    ); // by bounce i showed shady
    createOneWayTeleportFlag(
    (1474.59, 612.725, 560.125), 
    (1817.1, 537.948, 733.274) // to suiide from bounce spot i showed shady
    ); // top 
 }
setupTeleportsQuarry()
    {
        createOneWayTeleport( 
        (-5087.05, -90.4817, -191.875), // to top trickshot from door
        (-4739.48, -163.002, 352.125)
        );
        createOneWayTeleport(
        (-4831.08, -1404.36, -135.875),
        (-4864.68, -2941.59, 648.125)
        ); // by tank to up top inside map
        createOneWayTeleportFlag(
        (-5833.95, -2504.5, 103.194),
        (-4864.68, -2941.59, 648.125)
        ); // by tank to up top
       createOneWayTeleportFlag(
       (-5600.88, 1552.97, 84.5104),
       (-5866.05, 1082.09, 784.125) // by side of map to top quarry]
        ); 
        createOneWayTeleportFlag(
        (-4768.88, 2089.06, 200.004),
        (-5095.67, 1805.61, 484.955) // light post thing
        ); 
        createOneWayTeleportFlag(
        (-4079.08, -456.875, 80.125), 
        (-2731.6, -1970.15, 808.125)); // from indoor trickshot to by bonfire
        createTeleportPair(
        (-4079.06, 136.875, -55.875),
        (-1663.31, -823.015, 800.125)
        ); // from bonfire to quarry top
}

setupTeleportsScrapyard()
{
    // Create teleport pairs for Scrapyard
    createOneWayTeleportFlag(
    (2572.39, 2451.46, 61.4157),  // Position 1 (Scrapyard ground)
     (-2106.91, 626.546, 1125.36),   // Position 2 (Scrapyard upper)
    "truck",                 // Name 1
    "crane"                   // Name 2
    );
    
    createOneWayTeleportFlag(
    (-420.628, -10.1317, 1556.73),   // Position 1 (Scrapyard corner)
    (-4718.3, 4947.17, 1959.3),    // Position 2 (Scrapyard high spot)
    "crane",                        // Name 1
     "2nd crane"                      // Name 2
    );
    
    createOneWayTeleport(
        (2435.84, 652.71, -145.602),   // Entry point
        (2340.17, 650.023, -151.875),     // Exit point
        "door"              // Name
    );
    createOneWayTeleportFlag(
    (-4703.29, 4987.02, -831.875),  // Position 1
    (-4718.3, 4947.17, 1959.3),   // Position 2
    "under map",                  // Name 1
    "2nd crane"                    // Name 2
    );
    
    // Add more Scrapyard-specific pairs as needed
}

setupTeleportsInvasion(){
    createOneWayTeleportFlag(
    (1426.16, -2989.14, 360.594), // out of map
    (1165.69, -2822.94, 1270.13) // top main building
    );
    createOneWayTeleport(
    (-694.92, -4406.04, 519.125), // out of map 
    (-754.57, -4328.25, 1062.13) // across from B
    );
    createOneWayTeleport(
    (-2058.68, -1888.83, 268.125), // billboard floor 
    (-2221.03, -2069.55, 1020.13) // top billboard
    );
    createOneWayTeleport(
    (-2137.94, -2433.35, 264.125), // billboard floor on side
    (-2221.03, -2069.55, 1020.13) // top billboard
    );
    createOneWayTeleportFlag( 
    (-381.928, 919.757, 256.125), // out of map
    (-402.417, 1079.42, 256.125) // out of map
    );
    createTeleportPair(
    (-439.055, 1428.32, 256.125), // across bridge
    (-582.326, 2202.87, 256.125) // across bridge
    );
    createOneWayTeleport(
    (-647.897, -1063.63, 293.125), // by bomb door on closed off area
    (-523.622, -1159.03, 877.125) // on top of building bomb A
    );
    createOneWayTeleport(
    (-812.454, -1331.54, 285.125), // by bomb door on bomb side 
    (-523.622, -1159.03, 877.125) // on top of building bomb A
    );
    createOneWayTeleport(
    (788.334, -714.921, 430.125),
    (1075.58, -329.42, 384.125) // ledge to out of map
    );
    createOneWayTeleportFlag(
    (-3628.01, -2930.98, 262.759), // spawn and ele building area to out of map
    (-4682.45, -3165.24, 912.125)
    );
    createOneWayTeleport(
    (-4663.91, -2906.57, 360.093), // back to top from out of map building
    (-4682.45, -3165.24, 912.125)
    );
    createTeleportPair(
    (1126.45, -3971.71, 411.148), // getting accross fence
    (1307.13, -3787.31, 397.582)
    );
    createOneWayTeleport(
    (-185.839, -2576.87, 264.125), // door in courtyard area
    (-127.111, -2771.37, 846.125)
    );
    createOneWayTeleport(
    (-1223.13, -3677.78, 264.125), // broken house
    (-1419.59, -3758.37, 824.125) 
    );
    createOneWayTeleport(
    (-2259.4, -3629.98, 265.139),
    (-1958.18, -3748.75, 862.125)); // next to broken house
    
    
}

setupTeleportsTerminal()
{
   createOneWayTeleportFlag(
   (-880.526, 5198.85, 460.125),
   (-880.088, 4498.96, 719.262) // To outside of map on top of building
   );
   createOneWayTeleport(
   (-897.403, 4472.9, 44.125),
   (-880.088, 4498.96, 719.262) // under outside of map building to top of building
   );
   createOneWayTeleportFlag(
   (587.904, 2241.94, 40.125),
   (609.233, 2322.09, 572.21) // To outside of map on top of building
   );
}

setupTeleportsHighrise()
{
    createOneWayTeleport(
    (-77.3206, 5328.99, 2608.13),
    (-3757.44, 3065.12, 4400.13));
      createOneWayTeleport(
      (-2800.98, 7638.7, 2992.13),
      (-2298.07, 8786.3, 2851.93));
      createOneWayTeleport(
      (-476.881, 6696.27, 2806.08),
      (1463.89, 10411.2, 4064.13));
      createOneWayTeleport(
      (-6.68839, 5848.99, 2921.13),
      (102.144, 3287.34, 4100.13));
    
    
}

setupTeleportsUnderpass()
{
    createOneWayTeleport(
        (797.307, -496.975, 632.125),
        (-1575.4, 1884.11, 1947.82) // crane
    );
    createOneWayTeleport(
    (887.009, 2606.08, 370.582),
    (586.33, 2949.65, 1032.13) // grandma to bridge
    );
}

teleportPlayer(trigger)
{
    self endon("disconnect");
    
    if(!isDefined(trigger) || !isDefined(trigger.targetPos))
        return;
    
    // Set cooldown
    self.lastTeleportTime = getTime();
    
    // Play sound
    self playSound("mp_ingame_transition");
    
    // Teleport player
    if(isDefined(self))
    {
        // Store current weapon to prevent care package issues
        currentWeapon = self getCurrentWeapon();
        
        // Teleport the player
        self setOrigin(trigger.targetPos + (0, 0, 30));
        
        // Ensure player still has their weapon after teleport
        if(isDefined(currentWeapon) && currentWeapon != "none")
            self switchToWeapon(currentWeapon);
    }
}

setupTeleportsKarachi()
{
    // Create teleport pairs for Karachi
    createTeleportPair(
        (-4106.22, 1302.53, 0.124998),  // Position 1
        (-4171.86, 524.531, 24.4716),   // Position 2
        "Outside map",                  // Name 1
        "Gap"                    // Name 2
    ); 
    
    createTeleportPair(
    (-5289.03, -845.409, 82.7039),  // Position 1 (adjusted for Karachi)
    (-1758.18, -1913.24, -3.47841),   // Position 2 (adjusted for Karachi)
    "outside map",                        // Name 1
    "construction"                        // Name 2
        );
            
    createTeleportPair(
    (946.212, -3490.79, 424.125),  // Position 1 (adjusted for Karachi)
    (1420.57, -3800.5, 971.012),   // Position 2 (adjusted for Karachi)
        "Corner",                        // Name 1
        "Rooftop"                        // Name 2
    );
    
    createTeleportPair(
    (2368.71, -2714.56, 31.2219),  // Position 1 (adjusted for Karachi)
    (3340.73, -3.43392, 1.09864),   // Position 2 (adjusted for Karachi)
        "Corner",                        // Name 1
        "Rooftop"                        // Name 2
    );
    
    createTeleportPair(
    (-995.877, 1831.51, 288.125),  // Position 1 (adjusted for Karachi)
    (3174.47, 1145.01, 628.125),   // Position 2 (adjusted for Karachi)
        "Corner",                        // Name 1
        "Rooftop"                        // Name 2
    );
    
    createOneWayTeleport(
    (-4535.87, -82.234, 35.234),
    (-4795.04, -538.931, 928.125) // door to broken building
    );
    
    createOneWayTeleport(
    (-6759.58, 1124.34, 46.2009),
    (-7255.82, 1146.02, 934.125) // door to far back broken building
    );
    
    createOneWayTeleport(
    (-3883.14, 616.875, 49.8912),
    (-3690.04, 1102.82, 928.125) // door to building by flag 
    );
    
    createOneWayTeleport(
    (-2628.79, 588.923, 57.4398),
    (-2607.81, 923.185, 772.125) // door close to map to building by building by bomb site A
    );
    
    createOneWayTeleport(
    (-2745.38, -84.8979, 41.4728),
    (-2926.79, -649.17, 792.125) // door close to map to building by building by bomb site A other side
    );



    
    // You can add more Karachi-specific teleport pairs here
}


// Remove the entire monitorTeleports() function and the teleportPlayer(pad) function
// Keep only the getDistanceBetween function if it's used elsewhere
getDistanceBetween(point1, point2)
{
    if(!isDefined(point1) || !isDefined(point2))
        return 999999; // Return a large number if points aren't defined
        
    return sqrt(
        (point2[0] - point1[0]) * (point2[0] - point1[0]) +
        (point2[1] - point1[1]) * (point2[1] - point1[1]) +
        (point2[2] - point1[2]) * (point2[2] - point1[2])
    );
}


// Add this function after setupTeleports but before createTeleportPair
monitorTeleportActivation()
{
    level endon("game_ended");
    
    // We're not using these variables anymore but keeping them defined
    // to avoid errors in case other code references them
    level.teleportsActive = true;
    level.teleportActivationTime = 0;
    
    // This function now just exists to maintain compatibility
    // The actual teleport activation logic is in monitorTrigger
    while(true)
    {
        wait 5;
    }
}

createMapMarker(position)
{
    marker = spawnStruct();
    marker.position = position;
    marker.objID = maps\mp\gametypes\_gameobjects::getNextObjID();
    marker.shader = "compass_waypoint_panic";
    marker.shader = precacheShader(marker.shader);
    
    objective_add(marker.objID, "invisible", position);
    objective_position(marker.objID, position);
    objective_state(marker.objID, "invisible");
    objective_team(marker.objID, self.team);
    objective_icon(marker.objID, marker.shader);
    objective_onentity(marker.objID, position);
    
    return marker;
}


// Remove the monitorTeleportMarkers function since we're not using distance checks anymore

watchNearbyPlayers(teleportPos, objID)
{
    level endon("game_ended");
    
    while(1)
    {
        wait 0.1;
        showMarker = false;
        
        foreach(player in level.players)
        {
            if(!isDefined(player) || !isAlive(player))
                continue;
                
            if(distance(player.origin, teleportPos) < 500) // Adjust this distance as needed
            {
                showMarker = true;
                break;
            }
        }
        
        if(showMarker)
            objective_state(objID, "active");
        else
            objective_state(objID, "invisible");
    }
}

createOneWayTeleport(startPos, endPos, name) 
{
    trigger = spawn("trigger_radius", startPos, 0, 50, 50);
    if(!isDefined(trigger))
        return;
    
    trigger.targetPos = endPos;
    trigger.name = name;
    
    // Only create minimap marker, no flag
    trigger.objID = createMapMarker(startPos);
    
    trigger thread monitorTrigger();
}

createOneWayTeleportFlag(startPos, endPos, name) 
{
    // Create trigger at start position
    trigger = spawn("trigger_radius", startPos, 0, 50, 50);
    if(!isDefined(trigger))
        return;
    
    trigger.targetPos = endPos;
    trigger.name = name;
    
    // Create flag model at start position
    flagPos = startPos + (0, 0, 10); // Slightly raised to prevent clipping
    model = spawn("script_model", flagPos);
    if(isDefined(model))
    {
        model setModel("prop_flag_neutral");
        model.angles = (0, 0, 0);
        trigger.flagModel = model; // Store reference to flag
    }
    
    // Create minimap marker
    trigger.objID = createMapMarker(startPos);
    
    // Start monitoring the trigger
    trigger thread monitorTrigger();
}



// Example usage in a map setup function (add this to your map functions)

returnToMap()
{
    if(!(self isHost() || self.team == getHostTeam()))
        return;
    
    // Get current map name for debugging
    currentMap = getdvar("mapname");
    
    // Try spawn points first as they're guaranteed to be safe locations
    spawnPoints = getEntArray("mp_dm_spawn", "classname");
    if(spawnPoints.size == 0)
        spawnPoints = getEntArray("mp_tdm_spawn", "classname");
    if(spawnPoints.size == 0)
        spawnPoints = getEntArray("mp_sd_spawn", "classname");
        
    if(spawnPoints.size > 0)
    {
        // Sort spawn points by distance but also check enemy visibility
        safeLocations = [];
        
        foreach(spawn in spawnPoints)
        {
            distance = distance(self.origin, spawn.origin);
            
            // Check if any enemies can see this location
            isVisible = false;
            foreach(player in level.players)
            {
                if(player == self || !isAlive(player) || (level.teamBased && player.team == self.team))
                    continue;
                
                // Check if enemy has line of sight to this location
                trace = sightTracePassed(player getEye(), spawn.origin + (0, 0, 30), false, player);
                if(trace)
                {
                    isVisible = true;
                    break;
                }
            }
            
            // Only add to safe locations if not visible to enemies
            if(!isVisible)
            {
                // Check if the location is clear (not inside geometry)
                testPos = spawn.origin + (0, 0, 30);
                trace = bulletTrace(testPos + (0, 0, 50), testPos, false, self);
                
                if(trace["fraction"] == 1)
                {
                    safeLocations[safeLocations.size] = spawnStruct();
                    safeLocations[safeLocations.size-1].origin = spawn.origin;
                    safeLocations[safeLocations.size-1].angles = spawn.angles;
                    safeLocations[safeLocations.size-1].distance = distance;
                    safeLocations[safeLocations.size-1].type = "spawn";
                }
            }
        }
        
        // Sort safe locations by distance
        if(safeLocations.size > 0)
        {
            // Simple bubble sort by distance
            for(i = 0; i < safeLocations.size - 1; i++)
            {
                for(j = 0; j < safeLocations.size - i - 1; j++)
                {
                    if(safeLocations[j].distance > safeLocations[j+1].distance)
                    {
                        // Swap
                        temp = safeLocations[j];
                        safeLocations[j] = safeLocations[j+1];
                        safeLocations[j+1] = temp;
                    }
                }
            }
            
            // Use the closest safe location
            teleportLocation = safeLocations[0].origin;
            
            // Final check to make sure we're not spawning inside geometry
            groundTrace = bulletTrace(teleportLocation + (0, 0, 50), teleportLocation - (0, 0, 100), false, self);
            if(groundTrace["fraction"] < 1)
            {
                // Adjust height to be just above the ground
                teleportLocation = groundTrace["position"] + (0, 0, 5);
            }
            
            self setOrigin(teleportLocation);
            self setPlayerAngles(safeLocations[0].angles);
            self iPrintLnBold("^2Returned to safe spawn point!");
            
            // Close menu after teleporting
            if(isDefined(self.menuHUD))
            {
                destroyHUD();
                self.menuHUD = undefined;
            }
            return;
        }
    }
    
    // If no safe bomb sites found, try spawn points
    spawnPoints = getEntArray("mp_dm_spawn", "classname");
    if(spawnPoints.size == 0)
        spawnPoints = getEntArray("mp_tdm_spawn", "classname");
    if(spawnPoints.size == 0)
        spawnPoints = getEntArray("mp_sd_spawn", "classname");
        
    if(spawnPoints.size > 0)
    {
        // Sort spawn points by distance but also check enemy visibility
        safeLocations = [];
        
        foreach(spawn in spawnPoints)
        {
            distance = distance(self.origin, spawn.origin);
            
            // Check if any enemies can see this location
            isVisible = false;
            foreach(player in level.players)
            {
                if(player == self || !isAlive(player) || (level.teamBased && player.team == self.team))
                    continue;
                
                // Check if enemy has line of sight to this location
                trace = sightTracePassed(player getEye(), spawn.origin + (0, 0, 30), false, player);
                if(trace)
                {
                    isVisible = true;
                    break;
                }
            }
            
            // Only add to safe locations if not visible to enemies
            if(!isVisible)
            {
                safeLocations[safeLocations.size] = spawnStruct();
                safeLocations[safeLocations.size-1].origin = spawn.origin;
                safeLocations[safeLocations.size-1].angles = spawn.angles;
                safeLocations[safeLocations.size-1].distance = distance;
                safeLocations[safeLocations.size-1].type = "spawn";
            }
        }
        
        // Sort safe locations by distance
        if(safeLocations.size > 0)
        {
            // Simple bubble sort by distance
            for(i = 0; i < safeLocations.size - 1; i++)
            {
                for(j = 0; j < safeLocations.size - i - 1; j++)
                {
                    if(safeLocations[j].distance > safeLocations[j+1].distance)
                    {
                        // Swap
                        temp = safeLocations[j];
                        safeLocations[j] = safeLocations[j+1];
                        safeLocations[j+1] = temp;
                    }
                }
            }
            
            // Use the closest safe location
            teleportLocation = safeLocations[0].origin;
            self setOrigin(teleportLocation);
            self setPlayerAngles(safeLocations[0].angles);
            self iPrintLnBold("^2Returned to safe spawn point!");
            
            // Close menu after teleporting
            if(isDefined(self.menuHUD))
            {
                destroyHUD();
                self.menuHUD = undefined;
            }
            return;
        }
    }
    
    // If all else fails, try to find a safe spot around the player
    // similar to unstuckPlayer but with larger offsets to get back in bounds
    
    // Store current position and angles
 currentPos = self.origin;
    currentAngles = self getPlayerAngles();
    
    // Try different positions with increasing distances
    for(scale = 1; scale <= 10; scale++)
    {
        // Try different positions around the player to find a safe spot
        offsets = [];
        offsets[0] = (0, 0, 100 * scale);          // Above
        offsets[1] = (100 * scale, 0, 0);          // Forward
        offsets[2] = (-100 * scale, 0, 0);         // Back
        offsets[3] = (0, 100 * scale, 0);          // Right
        offsets[4] = (0, -100 * scale, 0);         // Left
        offsets[5] = (100 * scale, 100 * scale, 0); // Forward-Right
        offsets[6] = (100 * scale, -100 * scale, 0); // Forward-Left
        offsets[7] = (-100 * scale, 100 * scale, 0); // Back-Right
        offsets[8] = (-100 * scale, -100 * scale, 0); // Back-Left
        
        
        foreach(offset in offsets)
        {
            testPos = currentPos + offset;
            trace = bulletTrace(currentPos, testPos, false, self);
            
            // If we found a clear spot, check if it's inside the map
            if(trace["fraction"] == 1)
            {
                // Do a trace from high above to see if we're inside the map
                skyTrace = bulletTrace(testPos + (0, 0, 1000), testPos, false, self);
                
                // If the trace hits something, we're likely inside the map
                if(skyTrace["fraction"] < 1)
                {
                    // Check if any enemies can see this location
                    isVisible = false;
                    foreach(player in level.players)
                    {
                        if(player == self || !isAlive(player) || (level.teamBased && player.team == self.team))
                            continue;
                        
                        // Check if enemy has line of sight to this location
                        trace = sightTracePassed(player getEye(), testPos, false, player);
                        if(trace)
                        {
                            isVisible = true;
                            break;
                        }
                    }
                    
                    // Only teleport if not visible to enemies
                    if(!isVisible)
                    {
                        self setOrigin(testPos);
                        self setPlayerAngles(currentAngles);
                        self iPrintLnBold("^2Returned to map safely!");
                        
                        // Close menu after teleporting
                        if(isDefined(self.menuHUD))
                        {
                            destroyHUD();
                            self.menuHUD = undefined;
                        }
                        return;
                    }
                }
            }
        }
    }
    
    // Last resort - use the old map-specific teleport locations but still check visibility
    self iPrintLnBold("^3Using fallback teleport location...");
    self thread oldReturnToMapSafely();
}
// Modified version of oldReturnToMap that checks enemy visibility
oldReturnToMapSafely()
{
    // Get current map name
    currentMap = getdvar("mapname");
    potentialLocations = [];
    
    // Define strategic locations for each map (bomb sites or central locations)
    switch(currentMap)
    {
        case "mp_afghan":
            potentialLocations[0] = spawnStruct();
            potentialLocations[0].origin = (1100, 3500, -220);
            potentialLocations[0].angles = (0, 90, 0);
            
            potentialLocations[1] = spawnStruct();
            potentialLocations[1].origin = (800, 3200, -220);
            potentialLocations[1].angles = (0, 90, 0);
            break;
            
        case "mp_derail":
            potentialLocations[0] = spawnStruct();
            potentialLocations[0].origin = (-1500, 1500, 100);
            potentialLocations[0].angles = (0, 0, 0);
            
            potentialLocations[1] = spawnStruct();
            potentialLocations[1].origin = (-1200, 1200, 100);
            potentialLocations[1].angles = (0, 0, 0);
            break;
            
        // Add alternative locations for other maps...
        case "mp_highrise":
            potentialLocations[0] = spawnStruct();
            potentialLocations[0].origin = (170, 1950, 2620);
            potentialLocations[0].angles = (0, 180, 0);
            
            potentialLocations[1] = spawnStruct();
            potentialLocations[1].origin = (0, 1800, 2620);
            potentialLocations[1].angles = (0, 180, 0);
            break;
            
        default:
            // If map not recognized, try random spawn point
            spawnPoints = getEntArray("mp_dm_spawn", "classname");
            if(spawnPoints.size > 0)
            {
                // Add several random spawn points as potential locations
                for(i = 0; i < min(5, spawnPoints.size); i++)
                {
                    randomIndex = randomInt(spawnPoints.size);
                    potentialLocations[i] = spawnStruct();
                    potentialLocations[i].origin = spawnPoints[randomIndex].origin;
                    potentialLocations[i].angles = spawnPoints[randomIndex].angles;
                }
            }
            break;
    }
    
    // Check each location for enemy visibility
    safeLocation = undefined;
    
    foreach(location in potentialLocations)
    {
        isVisible = false;
        foreach(player in level.players)
        {
            if(player == self || !isAlive(player) || (level.teamBased && player.team == self.team))
                continue;
            
            // Check if enemy has line of sight to this location
            trace = sightTracePassed(player getEye(), location.origin, false, player);
            if(trace)
            {
                isVisible = true;
                break;
            }
        }
        
        if(!isVisible)
        {
            safeLocation = location;
            break;
        }
    }
    
    // Teleport the player if a safe location was found
    if(isDefined(safeLocation))
    {
        self setOrigin(safeLocation.origin);
        self setPlayerAngles(safeLocation.angles);
        self iPrintLnBold("^2Returned to map safely!");
        
        // Close menu after teleporting
        if(isDefined(self.menuHUD))
        {
            destroyHUD();
            self.menuHUD = undefined;
        }
    }
    else
    {
        // If no safe location found, just use the first location and warn the player
        if(potentialLocations.size > 0)
        {
            self setOrigin(potentialLocations[0].origin);
            self setPlayerAngles(potentialLocations[0].angles);
            self iPrintLnBold("^3Returned to map - may be visible to enemies!");
            
            // Close menu after teleporting
            if(isDefined(self.menuHUD))
            {
                destroyHUD();
                self.menuHUD = undefined;
            }
        }
        else
        {
            self iPrintLnBold("^1Could not find a safe location!");
        }
    }
}

unstuckPlayer()
{
    if(!(self isHost() || self.team == getHostTeam()))
        return;
        
    // Store current position and angles
    currentPos = self.origin;
    currentAngles = self getPlayerAngles();
    
    // Try different positions around the player to find a safe spot
    offsets = [];
    offsets[0] = (0, 0, 50);    // Above
    offsets[1] = (50, 0, 0);    // Forward
    offsets[2] = (-50, 0, 0);   // Back
    offsets[3] = (0, 50, 0);    // Right
    offsets[4] = (0, -50, 0);   // Left
    offsets[5] = (0, 0, -50);   // Below
    
    foreach(offset in offsets)
    {
        testPos = currentPos + offset;
        trace = bulletTrace(currentPos, testPos, false, self);
        
        // If we found a clear spot, teleport there
        if(trace["fraction"] == 1)
        {
            self setOrigin(testPos);
            self setPlayerAngles(currentAngles);
            self iPrintLnBold("^2Unstuck!");
            return;
        }
    }
    
    // If no safe spot found, try teleporting to spawn point
    spawnPoints = getEntArray("mp_dm_spawn", "classname");
    if(spawnPoints.size > 0)
    {
        randomSpawn = spawnPoints[randomInt(spawnPoints.size)];
        self setOrigin(randomSpawn.origin);
        self setPlayerAngles(randomSpawn.angles);
        self iPrintLnBold("^3Teleported to spawn point!");
    }
    else
    {
        self iPrintLnBold("^1Could not find safe position!");
    }
    }
toggleTeamTeleportMarkers()
{
    if(!self isHost())
        return;
    
    if(!isDefined(level.teleportMarkersEnabled))
        level.teleportMarkersEnabled = false;
    
    level.teleportMarkersEnabled = !level.teleportMarkersEnabled;
    
    if(level.teleportMarkersEnabled)
    {
        if(isDefined(level.teleportMarkers))
        {
            foreach(marker in level.teleportMarkers)
            {
                if(isDefined(marker) && isDefined(marker.objID))
                    objective_state(marker.objID, "active");
            }
        }
    }
    else
    {
        if(isDefined(level.teleportMarkers))
        {
            foreach(marker in level.teleportMarkers)
            {
                if(isDefined(marker) && isDefined(marker.objID))
                    objective_state(marker.objID, "invisible");
            }
        }
    }
    
    markerStatus = level.teleportMarkersEnabled ? "^2[ON]" : "^1[OFF]";
    newText = "Team Teleport Markers " + markerStatus;
    
    if(isDefined(self.LD1) && isDefined(self.LD1["host_page2"]))
    {
        for(i = 0; i < self.LD1["host_page2"]["opt"].size; i++)
        {
            if(isSubStr(self.LD1["host_page2"]["opt"][i], "Team Teleport Markers"))
            {
                self.LD1["host_page2"]["opt"][i] = newText;
                if(isDefined(self.menuHUD) && isDefined(self.menuHUD[i]))
                    self.menuHUD[i]["text"] setText(newText);
                break;
            }
        }
    }
    
    foreach(player in level.players)
    {
        if(player.team == self.team)
            player iPrintln("Team Teleport Markers: " + (level.teleportMarkersEnabled ? "^2ON" : "^1OFF"));
    }
}
