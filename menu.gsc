/*
*    Infinity Loader :: The Best GSC IDE!
*
*    Project : working trickshot patch
*    Author : 
*    Game : Call of Duty: Modern Warfare 2
*    Description : An empty canvas for anything you want!
*    Date : 3/14/2025 2:18:45 AM
*
*/

#include common_scripts\utility;
#include main;
#include utilities;
#include teleports;
#include bombs;
#include lobby;
#include killstreaks;
#include maps\mp\_airdrop;
#include maps\mp\_airDropNoPhysics;

menuStructure()
{
    if(isDefined(self.LD1))
        return;
    
    self.LD1 = [];
    
    // Main Menu
    self.LD1["main"] = [];
    self.LD1["main"]["title"] = "Main Menu";
    
    // Initialize status indicators
    if(!isDefined(self.pers["showDistanceFeed"]))
        self.pers["showDistanceFeed"] = 0;
    
    if(!isDefined(self.pers["showTeleportMarkers"]))
        self.pers["showTeleportMarkers"] = 0;
        
    distanceText = "Toggle Distance Feed ^1[OFF]";
    if(isDefined(self.pers["showDistanceFeed"]) && self.pers["showDistanceFeed"])
        distanceText = "Toggle Distance Feed ^2[ON]";
        
    teleportText = "Toggle Teleport Markers ^1[OFF]";
    if(isDefined(self.pers["showTeleportMarkers"]) && self.pers["showTeleportMarkers"])
        teleportText = "Toggle Teleport Markers ^2[ON]";
        
    ammoText = "Toggle Infinite Reserve" + ((!isDefined(self.pers["teamAmmoActive"]) || self.pers["teamAmmoActive"]) ? " ^2[ON]" : " ^1[OFF]");
    
    self.LD1["main"]["opt"] = [
        "Trickshot",  // Add this new option
        "Weapons",
        "Game Settings",
        "Return to Map", 
        "Unstuck",
        "More...", 
        "Exit"
    ];
    
    self.LD1["main"]["function"] = [
        ::openTrickshotMenu,  // Add this new function
        ::openWeaponsMenu,
        ::openGameSettings,
        ::returnToMap, 
        ::unstuckPlayer,
        ::openMoreMenu, 
        ::exitMenu
    ];
    
    self.LD1["main"]["input"] = ["", "", "", "", "", ""];
    self.LD1["main"]["parent"] = undefined;

    // Weapons Menu - Make sure this is defined in menuStructure
    self.LD1["weapons"] = [];
    self.LD1["weapons"]["title"] = "Weapons";
    self.LD1["weapons"]["opt"] = [
        "Snipers",
        "Assault Rifles",
        "SMGs",          // Added SMGs
        "Shotguns",
        "Pistols",
        "Launchers",
        "Back"
    ];
    self.LD1["weapons"]["function"] = [
        ::openSniperMenu,
        ::openARMenu,
        ::openSMGMenu,   // Added SMG function
        ::openShotgunMenu,
        ::openPistolMenu,
        ::openLauncherMenu,
        ::returnToParentMenu
    ];
    self.LD1["weapons"]["input"] = ["", "", "", "", "", "", ""];
    self.LD1["weapons"]["parent"] = "main";

    // SMGs Menu
    self.LD1["smgs"] = [];
    self.LD1["smgs"]["title"] = "SMGs";
    self.LD1["smgs"]["opt"] = [
        "MP5K",
        "UMP45",
        "Vector",
        "P90",
        "Mini-Uzi",
        "Back"
    ];
    self.LD1["smgs"]["function"] = [
        ::selectWeapon,
        ::selectWeapon,
        ::selectWeapon,
        ::selectWeapon,
        ::selectWeapon,
        ::returnToParentMenu
    ];
    self.LD1["smgs"]["input"] = [
        "mp5k_mp",
        "ump45_mp",
        "kriss_mp",
        "p90_mp",
        "uzi_mp",
        ""
    ];
    self.LD1["smgs"]["parent"] = "weapons";

    // Game Settings Menu
    self.LD1["game_settings"] = [];
    self.LD1["game_settings"]["title"] = "Game Settings";
    self.LD1["game_settings"]["opt"] = [
        ammoText,
        "Back"
    ];
    
    self.LD1["game_settings"]["function"] = [
        ::toggleTeamAmmo,
        ::returnToParentMenu
    ];
    
    self.LD1["game_settings"]["input"] = ["", "", ""];
    self.LD1["game_settings"]["parent"] = "main";

    // More Options Menu
    self.LD1["more"] = [];
    self.LD1["more"]["title"] = "More Options";

    self.LD1["more"]["opt"] = [
        "Killstreaks",
        "Bind Manager",
        "Host Menu",
        "Back"            // Moved to bottom
    ];

    self.LD1["more"]["function"] = [
        ::openKillstreakMenu,
        ::openBindMenu,
        ::openHostMenu,
        ::returnToParentMenu  // Moved to bottom
    ];

    self.LD1["more"]["input"] = ["", "", "", ""];
    self.LD1["more"]["parent"] = "main";

    // Host Menu
    self.LD1["host"] = [];
    self.LD1["host"]["title"] = "Host Menu";
    
    lockStatus = (getdvar("scr_sd_roundswitch") == "0") ? "^2[ON]" : "^1[OFF]";
    fastPlantStatus = (getdvar("scr_sd_planttime") == "0.1") ? "^2[ON]" : "^1[OFF]";
    
    self.LD1["host"]["opt"] = [
        "Force Side Switch",
        "Lock Sides " + lockStatus,
        "Fast Plant " + fastPlantStatus,
        "Destroy Killstreaks",
        "Switch Teams Menu",  // Add this new option
        "More Options",
        "Back"
    ];
    
    self.LD1["host"]["function"] = [
        ::forceSideSwitch,
        ::toggleSideLock,
        ::toggleFastPlant,
        ::destroyCounterUAVs,
        ::openSwitchTeamsMenu,  // Add this new function
        ::openHostPage2,
        ::returnToParentMenu
    ];
    
    self.LD1["host"]["input"] = ["", "", "", "", "", "", ""];
    self.LD1["host"]["parent"] = "main";
    
    // Host Menu Page 2 (modified)
    self.LD1["host_page2"] = [];
    self.LD1["host_page2"]["title"] = "Host Menu (2/2)";

    coordStatus = isDefined(self.showingCoords) ? "^2[ON]" : "^1[OFF]";

    self.LD1["host_page2"]["opt"] = [
        "Coordinates " + coordStatus,
        "Team Suicide",
        "Notify Bomber",
        "Back"
    ];

    self.LD1["host_page2"]["function"] = [
        ::toggleCoordinates,
        ::openSuicideConfirm,
        ::notifyBomber,
        ::returnToParentMenu
    ];

    self.LD1["host_page2"]["input"] = ["", "", "", ""];
    self.LD1["host_page2"]["parent"] = "host";
    
    // Killstreak Menu
    self.LD1["killstreaks"] = [];
    self.LD1["killstreaks"]["title"] = "Killstreaks";
    
    self.LD1["killstreaks"]["opt"] = [
        "UAV",
        "Care Package",
        "Predator Missile",
        "Tactical Nuke ^1[Host Only]",
        "Back"
    ];
    
    self.LD1["killstreaks"]["function"] = [
        ::giveUAV,
        ::giveCarePackage,
        ::givePredator,
        ::giveNuke,
        ::returnToParentMenu
    ];
    
    self.LD1["killstreaks"]["input"] = ["", "", "", "", ""];
    self.LD1["killstreaks"]["parent"] = "more";

    // Team Suicide Confirmation Menu
    self.LD1["suicide_confirm"] = [];
    self.LD1["suicide_confirm"]["title"] = "Confirm Team Suicide";
    
    self.LD1["suicide_confirm"]["opt"] = [
        "Yes",
        "No"
    ];
    
    self.LD1["suicide_confirm"]["function"] = [
        ::executeTeamSuicide,
        ::openHostMenu  // Changed from returnToParentMenu to openHostMenu
    ];
    
    self.LD1["suicide_confirm"]["input"] = ["", ""];
    self.LD1["suicide_confirm"]["parent"] = "host";

    // Snipers Menu
    self.LD1["snipers"] = [];
    self.LD1["snipers"]["title"] = "Sniper Rifles";
    self.LD1["snipers"]["opt"] = [
        "Intervention",
        "Barrett .50cal",
        "WA2000",
        "M21 EBR",
        "Back"
    ];
    self.LD1["snipers"]["function"] = [
        ::selectWeapon,
        ::selectWeapon,
        ::selectWeapon,
        ::selectWeapon,
        ::returnToParentMenu
    ];
    self.LD1["snipers"]["input"] = ["cheytac_mp", "barrett_mp", "wa2000_mp", "m21_mp", ""];
    self.LD1["snipers"]["parent"] = "weapons";

    // Pistols Menu
    self.LD1["pistols"] = [];
    self.LD1["pistols"]["title"] = "Pistols";
    self.LD1["pistols"]["opt"] = [
        "Desert Eagle",
        "Golden Desert Eagle",
        "Magnum .44",
        "M9",
        "USP .45",
        "Back"
    ];
    self.LD1["pistols"]["function"] = [
        ::selectPistol,
        ::selectPistol,
        ::selectPistol,
        ::selectPistol,
        ::selectPistol,
        ::returnToParentMenu
    ];
    self.LD1["pistols"]["input"] = [
        "deserteagle_mp",
        "deserteaglegold_mp",
        "coltanaconda_mp",
        "beretta_mp",
        "usp_mp",
        ""
    ];
    self.LD1["pistols"]["parent"] = "weapons"; // Removed the openPistolAttachments function call

    // Pistol Attachments Menu
    self.LD1["pistol_attachments"] = [];
    self.LD1["pistol_attachments"]["title"] = "Pistol Attachments";
    self.LD1["pistol_attachments"]["opt"] = [
        "No Attachment",
        "Tactical Knife",
        "Back"
    ];
    self.LD1["pistol_attachments"]["function"] = [
        ::setPistolAttachment,
        ::setPistolAttachment,
        ::returnToParentMenu
    ];
    self.LD1["pistol_attachments"]["input"] = ["none", "tactical", ""];
    self.LD1["pistol_attachments"]["parent"] = "pistols";

    // Assault Rifles Menu
    self.LD1["assault_rifles"] = [];
    self.LD1["assault_rifles"]["title"] = "Assault Rifles";
    self.LD1["assault_rifles"]["opt"] = [
        "ACR",
        "SCAR-H",
        "TAR-21",
        "FAMAS",
        "M16A4",
        "AK-47",
        "Back"
    ];
    self.LD1["assault_rifles"]["function"] = [
        ::selectWeapon,
        ::selectWeapon,
        ::selectWeapon,
        ::selectWeapon,
        ::selectWeapon,
        ::selectWeapon,
        ::returnToParentMenu
    ];
    self.LD1["assault_rifles"]["input"] = ["masada_mp", "scar_mp", "tavor_mp", "famas_mp", "m16_mp", "ak47_mp", ""];
    self.LD1["assault_rifles"]["parent"] = "weapons";

    // Shotguns Menu
    self.LD1["shotguns"] = [];
    self.LD1["shotguns"]["title"] = "Shotguns";
    self.LD1["shotguns"]["opt"] = [
        "SPAS-12",
        "Model 1887",
        "Ranger",
        "AA-12",
        "Back"
    ];
    self.LD1["shotguns"]["function"] = [
        ::selectWeapon,
        ::selectWeapon,
        ::selectWeapon,
        ::selectWeapon,
        ::returnToParentMenu
    ];
    self.LD1["shotguns"]["input"] = ["spas12_mp", "model1887_mp", "ranger_mp", "aa12_mp", ""];
    self.LD1["shotguns"]["parent"] = "weapons";

    // Launchers Menu
    self.LD1["launchers"] = [];
    self.LD1["launchers"]["title"] = "Launchers";
    self.LD1["launchers"]["opt"] = [
        "RPG-7",
        "AT4",
        "Back"
    ];
    self.LD1["launchers"]["function"] = [
        ::selectWeapon,
        ::selectWeapon,
        ::returnToParentMenu
    ];
    self.LD1["launchers"]["input"] = ["rpg_mp", "at4_mp", ""];
    self.LD1["launchers"]["parent"] = "weapons";

    // Equipment Menu
    self.LD1["equipment"] = [];
    self.LD1["equipment"]["title"] = "Equipment";
    self.LD1["equipment"]["opt"] = [
        "Throwing Knife",
        "Tactical Insertion",
        "Back"
    ];
    self.LD1["equipment"]["function"] = [
        ::giveEquipment,
        ::giveEquipment,
        ::returnToParentMenu
    ];
    self.LD1["equipment"]["input"] = [
        "throwing_knife_mp",
        "tactical_insertion_mp",
        ""
    ];
    self.LD1["equipment"]["parent"] = "weapons";

    // Create new Trickshot Menu
    self.LD1["trickshot"] = [];
    self.LD1["trickshot"]["title"] = "Trickshot Menu";

    // Initialize instashoot status
    if(!isDefined(self.pers["instashoot"]))
        self.pers["instashoot"] = 0;

    instashootText = "Instashoot ^1[OFF]";
    if(isDefined(self.pers["instashoot"]) && self.pers["instashoot"])
        instashootText = "Instashoot ^2[ON]";

    // Initialize canswap status
    if(!isDefined(self.pers["canswap"]))
        self.pers["canswap"] = 0;

    canswapText = "Always Canswap ^1[OFF]";
    if(isDefined(self.pers["canswap"]) && self.pers["canswap"])
        canswapText = "Always Canswap ^2[ON]";

    // Initialize cowboy status
    if(!isDefined(self.pers["cowboyActive"]))
        self.pers["cowboyActive"] = 0;

    cowboyText = "Cowboy ^1[OFF]";
    if(isDefined(self.pers["cowboyActive"]) && self.pers["cowboyActive"])
        cowboyText = "Cowboy ^2[ON]";

    self.LD1["trickshot"]["opt"] = [
        instashootText,
        canswapText,
        "Set One Bullet",
        "Drop ACR (Canswap)",
        "PredMala",
        "Cowboy",
        "Back"
    ];

    self.LD1["trickshot"]["function"] = [
        ::toggleFastWeaponSwitch,
        ::toggleCanswap,
        ::setOneBullet,
        ::dropCanswapACR,
        ::doPredMala,
        ::doCowboy,
        ::returnToParentMenu
    ];

    self.LD1["trickshot"]["input"] = ["", "", ""];
    self.LD1["trickshot"]["parent"] = "main";

    // Initialize bind manager menu
    self.LD1["bind_manager"] = [];
    self.LD1["bind_manager"]["title"] = "Bind Manager";
    self.LD1["bind_manager"]["opt"] = [];
    self.LD1["bind_manager"]["function"] = [];
    self.LD1["bind_manager"]["input"] = [];
    self.LD1["bind_manager"]["parent"] = "more";  // Set parent menu
    
    // Add default options
    self.LD1["bind_manager"]["opt"][0] = "Bind 1: Empty";
    self.LD1["bind_manager"]["opt"][1] = "Bind 2: Empty";
    self.LD1["bind_manager"]["opt"][2] = "Create New Bind";
    self.LD1["bind_manager"]["opt"][3] = "Remove All Binds";
    self.LD1["bind_manager"]["opt"][4] = "Back";
    
    // Add corresponding functions
    self.LD1["bind_manager"]["function"][0] = level.nullFunction;
    self.LD1["bind_manager"]["function"][1] = level.nullFunction;
    self.LD1["bind_manager"]["function"][2] = ::startBindProcess;
    self.LD1["bind_manager"]["function"][3] = level.removeAllBinds;
    self.LD1["bind_manager"]["function"][4] = ::returnToParentMenu;
}

openMoreMenu()
{
    destroyHUD();
    self.currentMenu = "more";
    self.menuCurs = isDefined(self.menuLastCurs[self.currentMenu]) ? self.menuLastCurs[self.currentMenu] : 0;
    createHUD(self.LD1[self.currentMenu]["opt"]);
}

openKillstreakMenu()
{
    destroyHUD();
    self.currentMenu = "killstreaks";
    self.menuCurs = isDefined(self.menuLastCurs[self.currentMenu]) ? self.menuLastCurs[self.currentMenu] : 0;
    createHUD(self.LD1[self.currentMenu]["opt"]);
}

// You can remove these functions as they're no longer needed:
// - getSideToggleText()
// - toggleSideSwitch()
// - setDefaultRounds()

// Keep these functions as they are:
// - forceSideSwitch()
// - toggleSideLock()
// - updateHostMenuOption()

createHUDItem(title, posX, posY, layer)
{
    hud = [];
    
    // Create the background with centered alignment
    hud["background"] = self createRectangle(
        "CENTER", "CENTER", posX, posY, 200, 20, (0, 0, 0), "white", layer, 0.9
    );
    
    // Create the highlight (purple) with centered alignment
    hud["highlight"] = self createRectangle(
        "CENTER", "CENTER", posX, posY, 200, 20, (0.5, 0, 0.8), "white", layer + 1, 0
    );
    
    // Create centered text
    hud["text"] = self createText(
        "hudBig", 0.4, "CENTER", "CENTER", posX, posY, layer + 2, 1, title, (1, 1, 1)
    );

    return hud;
}

updateMenuText(index, isOn)
{
    if(!isDefined(self.menuHUD) || !isDefined(self.currentMenu))
        return;
    
    // Update the menu text with new status based on current menu
    if(self.currentMenu == "game_settings")
    {
        baseText = index == 0 ? "Toggle Distance Feed" : "Toggle Team Ammo";
        self.LD1["game_settings"]["opt"][index] = baseText + (isOn ? " ^2[ON]" : " ^1[OFF]");
        
        // Update the displayed text if menu is open
        if(isDefined(self.menuHUD[index]))
            self.menuHUD[index]["text"] setText(self.LD1["game_settings"]["opt"][index]);
    }
}
// Add this function before createHUD
createEnemyCounter()
{
    // Create simple text element in top right
    self.enemyCounter                = self createText("objective", 1.4, "RIGHT", "TOP", 380, 20, 999, 1, "0", (1, 1, 1));
    self.enemyCounter.hideWhenInMenu = false;
}

// Update createHUD to actually call createEnemyCounter
createHUD(menuData)
{
    destroyHUD();
    wait 0.01;  // Back to 0.01 for stability
    
    self.menuHUD = [];
    self thread createEnemyCounter();

    // Add branding
    self.menuBranding = self createText(
        "hudBold",  // Bold font
        2.0,        // Large scale
        "LEFT",     // Left aligned
        "BOTTOM",   // Bottom aligned
        20,         // X position from left
        -20,        // Y position from bottom
        1,          // Layer
        0.25,       // Very low opacity (25%)
        "arkg0d's S&D",
        (1, 1, 1)   // White color
    );
    self.menuBranding.hideWhenInMenu = false;

    // Adjust starting Y position to be more centered
    startY = -80;
    
    // Title background - make it darker than menu options
    self.menuHUD["title"] = self createRectangle(
        "CENTER",
        "CENTER",
        0,
        startY,
        200,
        20,
        (0.2, 0, 0.3),  // Darker purple background
        "white",
        1,
        1
    );

    // Title text - centered alignment and slightly larger
    self.menuHUD["titleText"] = self createText(
        "hudBig",
        0.5,  // Slightly larger text
        "CENTER",  // Center align the text
        "CENTER",
        0,  // Center position (was -80)
        startY,
        2,
        1,
        self.LD1[self.currentMenu]["title"],
        (1, 0.832, 0)  // Keep the gold color
    );
    
    // Menu options start right after title
    for(i = 0; i < menuData.size; i++)
    {
        yOffset = startY + ((i + 1) * 20);
        
        self.menuHUD[i]["frame"] = self createRectangle(
            "CENTER",
            "CENTER",
            0,
            yOffset,
            200,
            20,
            (0, 0, 0),
            "white",
            i + 4,
            0.85
        );

        self.menuHUD[i]["text"] = self createText(
            "hudBig",
            0.4,
            "LEFT",
            "CENTER",
            -80,
            yOffset,
            i + 6,
            1,
            menuData[i],
            (1, 1, 1)
        );

        self.menuHUD[i]["highlight"] = self createRectangle(
            "CENTER",
            "CENTER",
            0,
            yOffset,
            200,
            20,
            (0.4, 0, 0.6),
            "white",
            i + 5,
            0
        );
    }

    if(menuData.size > 0)
        highlightMenuOption(self.menuCurs, true);
}

destroyHUD()
{
    if(isDefined(self.bindText))
    {
        self.bindText destroy();
        self.bindText = undefined;
    }
    
    if(isDefined(self.menuHUD))
    {
        if(isDefined(self.menuHUD["titleText"]))
            self.menuHUD["titleText"] destroy();
        if(isDefined(self.menuHUD["title"]))
            self.menuHUD["title"] destroy();
            
        for(i = 0; i < self.menuHUD.size; i++)
        {
            if(isDefined(self.menuHUD[i]))
            {
                if(isDefined(self.menuHUD[i]["text"]))
                    self.menuHUD[i]["text"] destroy();
                if(isDefined(self.menuHUD[i]["highlight"]))
                    self.menuHUD[i]["highlight"] destroy();
                if(isDefined(self.menuHUD[i]["frame"]))
                    self.menuHUD[i]["frame"] destroy();
                self.menuHUD[i] destroy();
            }
        }
    }
    
    // Destroy branding
    if(isDefined(self.menuBranding))
        self.menuBranding destroy();
        
    if(isDefined(self.enemyCounter))
        self.enemyCounter destroy();
        
    wait 0.01;  // Ensure cleanup completes
}

highlightMenuOption(index, highlight)
{
    for(i = 0; i < self.menuHUD.size; i++)
    {
        hudItem = self.menuHUD[i];
        if(i == index && highlight)
        {
            hudItem["highlight"].alpha = 1;
            hudItem["text"].color = (1, 1, 1);
        }
        else
        {
            hudItem["highlight"].alpha = 0;
            hudItem["text"].color = (1, 1, 1);
        }
    }
}


createModMenu()
{
    self endon("disconnect");
    
    self thread menuStructure();
    
    for(;;)
    {
        if(isDefined(self.menuHUD))
        {
            destroyHUD();
            self.menuHUD = undefined;
            wait 0.01;
        }

        hostTeam = getHostTeam();
        
        if(!(self isHost() || self.pers["team"] == hostTeam))
        {
            wait 0.05;
            continue;
        }

        if(self AdsButtonPressed() && self MeleeButtonPressed())
        {
            // Reset binding state when opening menu
            self.isBinding = undefined;
            if(isDefined(self.bindText))
            {
                self.bindText destroy();
                self.bindText = undefined;
            }
            
            self.currentMenu = "main";
            self.menuCurs = isDefined(self.menuLastCurs[self.currentMenu]) ? self.menuLastCurs[self.currentMenu] : 0;
            createHUD(self.LD1[self.currentMenu]["opt"]);
            
            self thread updateEnemyCounter();
            
            wait 0.2;

            while(isDefined(self.menuHUD))
            {
                if(self isDpadDownPressed())
                {
                    highlightMenuOption(self.menuCurs, false);
                    self.menuCurs++;
                    if(self.menuCurs >= self.LD1[self.currentMenu]["opt"].size)
                        self.menuCurs = 0;
                    self.menuLastCurs[self.currentMenu] = self.menuCurs;
                    highlightMenuOption(self.menuCurs, true);
                    wait 0.2;
                }

                if(self isDpadUpPressed())
                {
                    highlightMenuOption(self.menuCurs, false);
                    self.menuCurs--;
                    if(self.menuCurs < 0)
                        self.menuCurs = self.LD1[self.currentMenu]["opt"].size - 1;
                    self.menuLastCurs[self.currentMenu] = self.menuCurs;
                    highlightMenuOption(self.menuCurs, true);
                    wait 0.2;
                }

                if(self UseButtonPressed())
                {
                    if(isDefined(self.isSelectingFunction) && self.isSelectingFunction)
                    {
                        currentFunc = self.LD1[self.currentMenu]["function"][self.menuCurs];
                        // Only allow binding of actual functions, not menu navigation
                        if(currentFunc != ::openMoreMenu && 
                           currentFunc != ::openHostMenu && 
                           currentFunc != ::returnToParentMenu)
                        {
                            self selectBindFunction();
                        }
                        else
                        {
                            // Allow normal menu navigation while selecting function
                            self thread [[currentFunc]](self.LD1[self.currentMenu]["input"][self.menuCurs]);
                        }
                    }
                    else
                    {
                        // Normal menu handling
                        self thread [[self.LD1[self.currentMenu]["function"][self.menuCurs]]](self.LD1[self.currentMenu]["input"][self.menuCurs]);
                    }
                    wait 0.18;
                }

                if(self MeleeButtonPressed())
                {
                    if(isDefined(self.LD1[self.currentMenu]["parent"]))
                    {
                        oldMenu = self.currentMenu;
                        parentMenu = self.LD1[self.currentMenu]["parent"];
                        self.menuLastCurs[self.currentMenu] = self.menuCurs;
                        self.currentMenu = parentMenu;
                        
                        if(oldMenu == "host")
                        {
                            for(i = 0; i < self.LD1[parentMenu]["opt"].size; i++)
                            {
                                if(self.LD1[parentMenu]["opt"][i] == "Host Menu")
                                {
                                    self.menuCurs = i;
                                    self.menuLastCurs[parentMenu] = i;
                                    break;
                                }
                            }
                        }
                        else
                            self.menuCurs = isDefined(self.menuLastCurs[parentMenu]) ? self.menuLastCurs[parentMenu] : 0;
                        
                        createHUD(self.LD1[self.currentMenu]["opt"]);
                        wait 0.18;
                    }
                    else
                    {
                        self notify("stop_counter_update");
                        destroyHUD();
                        self.menuHUD = undefined;
                        wait 0.18;
                        break;
                    }
                }

                wait 0.01;
            }
        }
        wait 0.05;
    }
}

processDpadMoves(direction)
{
    highlightMenuOption(self.menuCurs, false);
    
    if(direction == "down")
    {
        self.menuCurs++;
        if(self.menuCurs >= self.LD1[self.currentMenu]["opt"].size)
            self.menuCurs = 0;
    }
    else
    {
        self.menuCurs--;
        if(self.menuCurs < 0)
            self.menuCurs = self.LD1[self.currentMenu]["opt"].size - 1;
    }
    
    self.menuLastCurs[self.currentMenu] = self.menuCurs;
    highlightMenuOption(self.menuCurs, true);
}

// Add new function to update enemy counter
updateEnemyCounter()
{
    self endon("disconnect");
    self endon("stop_counter_update");
    
    while(isDefined(self.menuHUD))
    {
        hostTeam = getHostTeam();
        if(hostTeam != "")
        {
            enemyCount = 0;
            enemyTeam = (hostTeam == "allies") ? "axis" : "allies";
            
            foreach(player in level.players)
            {
                if(player.sessionstate == "playing" && isDefined(player.pers["team"]))
                {
                    if(player.team == enemyTeam && isAlive(player))
                        enemyCount++;
                }
            }
            
            if(isDefined(self.enemyCounter))
                self.enemyCounter setText(enemyCount);
        }
        wait 0.05;
    }
}
// Example toggle functions
toggleOption1()
{
    self.option1 = isDefined(self.option1) ? undefined : true;
    self iprintln("Option 1: " + (isDefined(self.option1) ? "^2ON" : "^1OFF"));
}

toggleOption2()
{
    self.option2 = isDefined(self.option2) ? undefined : true;
    self iprintln("Option 2: " + (isDefined(self.option2) ? "^2ON" : "^1OFF"));
}

toggleOption3()
{
    self.option3 = isDefined(self.option3) ? undefined : true;
    self iprintln("Option 3: " + (isDefined(self.option3) ? "^2ON" : "^1OFF"));
}

exitMenu()
{
    self iprintln("Menu Closed");
    destroyHUD();
    self.menuHUD = undefined;
}

// Add these new functions
openHostMenu()
{
    if(!self isHost())
        return;
    
    destroyHUD();
    self.currentMenu = "host";
    self.menuCurs = isDefined(self.menuLastCurs[self.currentMenu]) ? self.menuLastCurs[self.currentMenu] : 0;
    
    // Update existing host menu options
    lockStatus = (getdvar("scr_sd_roundswitch") == "0") ? "^2[ON]" : "^1[OFF]";
    fastPlantStatus = (getdvar("scr_sd_planttime") == "0.1") ? "^2[ON]" : "^1[OFF]";
    
    self.LD1["host"]["opt"] = [
        "Force Side Switch",
        "Lock Sides " + lockStatus,
        "Fast Plant " + fastPlantStatus,
        "Destroy Killstreaks",
        "Switch Teams Menu",  // Add this new option
        "More Options",
        "Back"
    ];
    
    self.LD1["host"]["function"] = [
        ::forceSideSwitch,
        ::toggleSideLock,
        ::toggleFastPlant,
        ::destroyCounterUAVs,
        ::openSwitchTeamsMenu,  // Add this new function
        ::openHostPage2,
        ::returnToParentMenu
    ];
    
    self.LD1["host"]["input"] = ["", "", "", "", "", "", ""];
    
    createHUD(self.LD1[self.currentMenu]["opt"]);
}

returnToParentMenu()
{
    if(!isDefined(self.menuHUD))
        return;
        
    parentMenu = self.LD1[self.currentMenu]["parent"];
    if(!isDefined(parentMenu))
        return;
    
    // Clear current menu completely
    destroyHUD();
    
    // Reset to parent menu
    self.currentMenu = parentMenu;
    self.menuCurs = isDefined(self.menuLastCurs[self.currentMenu]) ? self.menuLastCurs[self.currentMenu] : 0;
    
    // Create fresh HUD for parent menu
    createHUD(self.LD1[self.currentMenu]["opt"]);
    
    // If we're in binding mode, update the title
    if(isDefined(self.isBinding) && self.isBinding)
    {
        if(isDefined(self.menuHUD["titleText"]))
            self.menuHUD["titleText"] setText("Select Function to Bind");
        if(isDefined(self.menuHUD["title"]))
            self.menuHUD["title"].color = (0.4, 0, 0.3);
    }
}

// Example host menu functions (replace with actual functionality)
hostOption1()
{
    // Add your host-only functionality here
}

hostOption2()
{
    // Add your host-only functionality here
}

forceSideSwitch()
{
    if(!self isHost())
        return;
    
    // Immediately switch sides
    game["switchedsides"] = !game["switchedsides"];
    level notify("switch_sides");
    
    // If sides are already locked, maintain the lock
    if(getdvar("scr_sd_roundswitch") == "0")
    {
        setdvar("scr_sd_roundswitch", "0");  // Ensure it stays locked
    }
    
    // Notify only the host
    self iprintln("^3Sides have been switched");
}

toggleSideLock()
{
    if(!self isHost())
        return;
    
    currentValue = getdvar("scr_sd_roundswitch");
    if(currentValue != "0")
    {
        // Lock sides in current configuration
        setdvar("scr_sd_roundswitch", "0");
        self updateHostMenuOption(1, "Lock Sides ^2[ON]");
        self iprintln("^3Sides are now locked in current configuration");
    }
    else
    {
        // Unlock sides (return to default behavior)
        setdvar("scr_sd_roundswitch", "2");
        self updateHostMenuOption(1, "Lock Sides ^1[OFF]");
        self iprintln("^3Sides will switch normally");
    }
}

updateHostMenuOption(index, newText)
{
    if(!isDefined(self.menuHUD) || !isDefined(self.LD1["host"]["opt"]))
        return;
    
    // Update the menu option text
    self.LD1["host"]["opt"][index] = newText;
    
    // Update the displayed text if menu is open
    if(isDefined(self.menuHUD[index]))
        self.menuHUD[index]["text"] setText(newText);
}

openSuicideConfirm()
{
    destroyHUD();
    self.currentMenu = "suicide_confirm";
    self.menuCurs = isDefined(self.menuLastCurs[self.currentMenu]) ? self.menuLastCurs[self.currentMenu] : 0;
    createHUD(self.LD1[self.currentMenu]["opt"]);
}

executeTeamSuicide()
{
    if(!self isHost())
        return;
        
    hostTeam = getHostTeam();
    foreach(player in level.players)
    {
        if(player.pers["team"] == hostTeam)
        {
            player suicide();
        }
    }
    
    // Close menu completely
    destroyHUD();
    self.menuHUD = undefined;
}

selectWeapon()
{
    weaponName = self.LD1[self.currentMenu]["input"][self.menuCurs];
    currentWeapon = self getCurrentWeapon();
    
    // Take away current weapon first
    if(isDefined(currentWeapon) && currentWeapon != "none")
        self takeWeapon(currentWeapon);
    
    self giveWeapon(weaponName);
    self giveMaxAmmo(weaponName);
    self switchToWeapon(weaponName);
    
    weaponText = self.LD1[self.currentMenu]["opt"][self.menuCurs];
    self iPrintLn("Weapon given: " + weaponText);
    
    self closeMenu();
}

selectPistol()
{
    self.selectedPistol = self.LD1[self.currentMenu]["input"][self.menuCurs];
    self.selectedPistolName = self.LD1[self.currentMenu]["opt"][self.menuCurs];
    self openPistolAttachments();
}

buildWeaponName(baseName, attachment1, attachment2)
{
    if(!isDefined(attachment1) || attachment1 == "none")
        attachment1 = "none";
    if(!isDefined(attachment2) || attachment2 == "none")
        attachment2 = "none";

    if(attachment1 == "none" && attachment2 == "none")
        return baseName;
        
    if(attachment2 == "none")
        return baseName + "_" + attachment1;
        
    return baseName + "_" + attachment1 + "_" + attachment2;
}

setPistolAttachment(attachment)
{
    baseWeapon = self.selectedPistol;
    attachmentType = self.LD1[self.currentMenu]["input"][self.menuCurs];
    currentWeapon = self getCurrentWeapon();
    
    // Take away current weapon first
    if(isDefined(currentWeapon) && currentWeapon != "none")
        self takeWeapon(currentWeapon);
    
    if(attachmentType == "none")
    {
        weaponName = baseWeapon;
    }
    else if(attachmentType == "tactical")
    {
        baseWithoutMp = getSubStr(baseWeapon, 0, baseWeapon.size - 3);
        weaponName = baseWithoutMp + "_tactical_mp";
    }
    
    self giveWeapon(weaponName);
    self giveMaxAmmo(weaponName);
    self switchToWeapon(weaponName);
    
    attachmentText = (attachmentType == "none") ? "" : " with " + self.LD1[self.currentMenu]["opt"][self.menuCurs];
    self iPrintLn("Weapon given: " + self.selectedPistolName + attachmentText);
    
    self closeMenu();
}

getBaseWeaponName(fullWeaponName)
{
    // Special case for .44 Magnum
    if(fullWeaponName == "44magnum_mp")
        return "44magnum";
        
    // Remove "_mp" from the end for other weapons
    return getSubStr(fullWeaponName, 0, fullWeaponName.size - 3);
}

// Add these menu opening functions
openWeaponsMenu()
{
    self.menuLastCurs[self.currentMenu] = self.menuCurs;
    self.currentMenu = "weapons";
    self.menuCurs = isDefined(self.menuLastCurs[self.currentMenu]) ? self.menuLastCurs[self.currentMenu] : 0;
    destroyHUD();
    createHUD(self.LD1[self.currentMenu]["opt"]);
}

openSniperMenu()
{
    destroyHUD();
    self.currentMenu = "snipers";
    self.menuCurs = isDefined(self.menuLastCurs[self.currentMenu]) ? self.menuLastCurs[self.currentMenu] : 0;
    createHUD(self.LD1[self.currentMenu]["opt"]);
}

openPistolMenu()
{
    destroyHUD();
    self.currentMenu = "pistols";
    self.menuCurs = isDefined(self.menuLastCurs[self.currentMenu]) ? self.menuLastCurs[self.currentMenu] : 0;
    createHUD(self.LD1[self.currentMenu]["opt"]);
}

openARMenu()
{
    destroyHUD();
    self.currentMenu = "assault_rifles";
    self.menuCurs = isDefined(self.menuLastCurs[self.currentMenu]) ? self.menuLastCurs[self.currentMenu] : 0;
    createHUD(self.LD1[self.currentMenu]["opt"]);
}

openShotgunMenu()
{
    destroyHUD();
    self.currentMenu = "shotguns";
    self.menuCurs = isDefined(self.menuLastCurs[self.currentMenu]) ? self.menuLastCurs[self.currentMenu] : 0;
    createHUD(self.LD1[self.currentMenu]["opt"]);
}

openLauncherMenu()
{
    destroyHUD();
    self.currentMenu = "launchers";
    self.menuCurs = isDefined(self.menuLastCurs[self.currentMenu]) ? self.menuLastCurs[self.currentMenu] : 0;
    createHUD(self.LD1[self.currentMenu]["opt"]);
}

openSMGMenu()
{
    destroyHUD();
    self.currentMenu = "smgs";
    self.menuCurs = isDefined(self.menuLastCurs[self.currentMenu]) ? self.menuLastCurs[self.currentMenu] : 0;
    createHUD(self.LD1[self.currentMenu]["opt"]);
}

openGameSettings()
{
    destroyHUD();
    self.currentMenu = "game_settings";
    self.menuCurs = isDefined(self.menuLastCurs[self.currentMenu]) ? self.menuLastCurs[self.currentMenu] : 0;
    createHUD(self.LD1[self.currentMenu]["opt"]);
}

giveEquipment(equipment)
{
    equipmentName = self.LD1[self.currentMenu]["input"][self.menuCurs];
    
    // Fix equipment names if needed
    switch(equipmentName)
    {
        case "throwingknife_mp":
            equipmentName = "throwing_knife_mp";
            break;
        case "tacinsert_mp":
            equipmentName = "tactical_insertion_mp";
            break;
    }
    
    // Only replace equipment slot
    foreach(weapon in self GetWeaponsList())
    {
        if(isSubStr(weapon, "throwing_knife") || isSubStr(weapon, "tactical_insertion"))
            self takeWeapon(weapon);
    }
    
    self giveWeapon(equipmentName);
    self iPrintLn("Equipment given: " + self.LD1[self.currentMenu]["opt"][self.menuCurs]);
    self closeMenu();
}

openPistolAttachments()
{
    if(self.selectedPistol == "deserteaglegold_mp")
    {
        currentWeapon = self getCurrentWeapon();
        if(isDefined(currentWeapon) && currentWeapon != "none")
            self takeWeapon(currentWeapon);
            
        self giveWeapon(self.selectedPistol);
        self giveMaxAmmo(self.selectedPistol);
        self switchToWeapon(self.selectedPistol);
        
        self iPrintLn("Weapon given: " + self.selectedPistolName);
        self closeMenu();
        return;
    }

    destroyHUD();
    self.currentMenu = "pistol_attachments";
    self.menuCurs = isDefined(self.menuLastCurs[self.currentMenu]) ? self.menuLastCurs[self.currentMenu] : 0;
    
    self.LD1["pistol_attachments"] = [];
    self.LD1["pistol_attachments"]["title"] = "Pistol Attachments";
    self.LD1["pistol_attachments"]["opt"] = [
        "No Attachment",
        "Tactical Knife",
        "Back"
    ];
    self.LD1["pistol_attachments"]["input"] = [
        "none",
        "tactical",
        ""
    ];
    
    self.LD1["pistol_attachments"]["function"] = [
        ::setPistolAttachment,
        ::setPistolAttachment,
        ::returnToParentMenu
    ];
    
    self.LD1["pistol_attachments"]["parent"] = "pistols";
    
    createHUD(self.LD1[self.currentMenu]["opt"]);
}

notifyBomber()
{
    bombCarrier = undefined;
    
    foreach(player in level.players)
    {
        if(isDefined(player.isBombCarrier) && player.isBombCarrier)
        {
            bombCarrier = player;
            break;
        }
    }
    
    if(isDefined(bombCarrier))
    {
        bombCarrier iPrintLnBold("You have bomb...");
        self iPrintln("Message sent to bomb carrier: " + bombCarrier.name);
    }
    else
    {
        self iPrintln("No bomb carrier found!");
    }
}

openHostPage2()
{
    destroyHUD();
    self.currentMenu = "host_page2";
    self.menuCurs = isDefined(self.menuLastCurs[self.currentMenu]) ? self.menuLastCurs[self.currentMenu] : 0;
    createHUD(self.LD1[self.currentMenu]["opt"]);
}

destroyCounterUAVs()
{
    if(!self isHost())
        return;
        
    // Use the same logic as EMP to destroy all killstreaks
    foreach(heli in level.helis)
        radiusDamage(heli.origin, 384, 5000, 5000, self);

    foreach(littleBird in level.littleBird)
        radiusDamage(littleBird.origin, 384, 5000, 5000, self);
        
    foreach(turret in level.turrets)
        radiusDamage(turret.origin, 16, 5000, 5000, self);

    foreach(rocket in level.rockets)
        rocket notify("death");
        
    if(level.teamBased)
    {
        foreach(uav in level.uavModels["allies"])
            radiusDamage(uav.origin, 384, 5000, 5000, self);

        foreach(uav in level.uavModels["axis"])
            radiusDamage(uav.origin, 384, 5000, 5000, self);
    }
    else
    {    
        foreach(uav in level.uavModels)
            radiusDamage(uav.origin, 384, 5000, 5000, self);
    }
    
    self iPrintln("^2Enemy Killstreaks Destroyed");
}


// Add this new function
openTrickshotMenu()
{
    self.menuLastCurs[self.currentMenu] = self.menuCurs;
    self.currentMenu = "trickshot";
    self.menuCurs = isDefined(self.menuLastCurs[self.currentMenu]) ? self.menuLastCurs[self.currentMenu] : 0;
    destroyHUD();
    createHUD(self.LD1[self.currentMenu]["opt"]);
}

// Modify the dropCanswapACR function to properly spawn the weapon
dropCanswapACR()
{
    weapon = "masada_mp";  // ACR's internal name
    
    self GiveWeapon(weapon);
    self SwitchToWeapon(weapon);
    self DropItem(weapon);
    
    self iPrintln("ACR dropped for canswap");
}

checkBindCombination()
{
    stance = self getStance();
    
    // Check each dpad + stance combination
    if(self isDpadUpPressed() && (stance == "stand" || stance == "crouch" || stance == "prone"))
        return stance + "_dpadup";
        
    if(self isDpadDownPressed() && (stance == "stand" || stance == "crouch" || stance == "prone"))
        return stance + "_dpaddown";
        
    if(self isDpadLeftPressed() && (stance == "stand" || stance == "crouch" || stance == "prone"))
        return stance + "_dpadleft";
        
    if(self isDpadRightPressed() && (stance == "stand" || stance == "crouch" || stance == "prone"))
        return stance + "_dpadright";
        
    return undefined;
}

initBindSystem()
{
    // Initialize bind storage in persistent data
    if(!isDefined(self.pers["binds"]))
        self.pers["binds"] = [];
    
    // Copy from persistent storage to temporary storage for menu system
    if(!isDefined(self.binds))
        self.binds = self.pers["binds"];
}

openBindMenu()
{
    destroyHUD();
    self.currentMenu = "bind_manager";
    self.menuCurs = isDefined(self.menuLastCurs[self.currentMenu]) ? self.menuLastCurs[self.currentMenu] : 0;
    createHUD(self.LD1[self.currentMenu]["opt"]);
}

updateBindMenuOptions()
{
    if(!isDefined(self.LD1["bind_manager"]))
        return;
        
    options = [];
    functions = [];
    
    // Show current binds
    if(isDefined(self.binds) && isDefined(self.binds[0]))
        options[options.size] = "Bind 1: " + self.binds[0]["name"] + " - " + self.binds[0]["combo"];
    else
        options[options.size] = "Bind 1: Empty";
        
    if(isDefined(self.binds) && isDefined(self.binds[1]))
        options[options.size] = "Bind 2: " + self.binds[1]["name"] + " - " + self.binds[1]["combo"];
    else
        options[options.size] = "Bind 2: Empty";
    
    // Add create/remove options
    if(!isDefined(self.binds) || !isDefined(self.binds[0]) || !isDefined(self.binds[1]))
        options[options.size] = "Create New Bind";
        
    options[options.size] = "Remove All Binds";
    options[options.size] = "Back";
    
    // Set up corresponding functions
    for(i = 0; i < options.size; i++)
    {
        if(i < 2 && isDefined(self.binds) && isDefined(self.binds[i]))
            functions[i] = level.removeBind;
        else if(options[i] == "Create New Bind")
            functions[i] = ::startBindProcess;
        else if(options[i] == "Remove All Binds")
            functions[i] = level.removeAllBinds;
        else if(options[i] == "Back")
            functions[i] = ::returnToParentMenu;
        else
            functions[i] = level.nullFunction;
    }
    
    // Update menu data
    self.LD1["bind_manager"]["opt"] = options;
    self.LD1["bind_manager"]["function"] = functions;
    self.LD1["bind_manager"]["input"] = [];
    self.LD1["bind_manager"]["parent"] = "more";
}

startBindProcess()
{
    destroyHUD();
    
    // Create bind setup menu
    self.LD1["bind_setup"] = [];
    self.LD1["bind_setup"]["title"] = "Configure Bind";
    
    // Get display texts
    functionText = isDefined(self.tempBindFunction) ? "Function: " + self.tempBindName : "Function: Not Set";
    stanceText = isDefined(self.tempBindStance) ? "Stance: " + self.tempBindStance : "Stance: Not Set";
    dpadText = isDefined(self.tempBindDpad) ? "D-Pad: " + self.tempBindDpad : "D-Pad: Not Set";
    
    self.LD1["bind_setup"]["opt"] = [
        functionText,
        stanceText,
        dpadText,
        "Save Bind",
        "Cancel"
    ];
    
    self.LD1["bind_setup"]["function"] = [
        ::startFunctionSelection,
        ::openStanceMenu,
        ::openDpadMenu,
        ::saveBind,
        ::cancelBind
    ];
    
    self.LD1["bind_setup"]["input"] = [];
    self.LD1["bind_setup"]["parent"] = "bind_manager";
    
    // Switch to setup menu
    self.currentMenu = "bind_setup";
    self.menuCurs = 0;
    createHUD(self.LD1[self.currentMenu]["opt"]);
}

startFunctionSelection()
{
    // Switch to main menu for function selection
    self.menuLastCurs[self.currentMenu] = self.menuCurs;
    self.currentMenu = "main";
    self.menuCurs = isDefined(self.menuLastCurs[self.currentMenu]) ? self.menuLastCurs[self.currentMenu] : 0;
    
    // Enable function selection mode
    self.isSelectingFunction = true;
    
    // Create HUD
    destroyHUD();
    createHUD(self.LD1[self.currentMenu]["opt"]);
}

selectBindFunction()
{
    currentFunc = self.LD1[self.currentMenu]["function"][self.menuCurs];
    
    // Special case for pistol selection - must go through attachments first
    if(self.currentMenu == "pistols" && 
       self.LD1[self.currentMenu]["input"][self.menuCurs] != "deserteaglegold_mp") // Special case for gold deagle
    {
        // Store the selected pistol and open attachments
        self.selectedPistol = self.LD1[self.currentMenu]["input"][self.menuCurs];
        self.selectedPistolName = self.LD1[self.currentMenu]["opt"][self.menuCurs];
        self openPistolAttachments();
        return;
    }
    
    // If we're in pistol attachments menu
    if(self.currentMenu == "pistol_attachments")
    {
        // Store weapon data for binding
        attachmentType = self.LD1[self.currentMenu]["input"][self.menuCurs];
        
        if(attachmentType == "none")
        {
            weaponName = self.selectedPistol;
        }
        else if(attachmentType == "tactical")
        {
            baseWithoutMp = getSubStr(self.selectedPistol, 0, self.selectedPistol.size - 3);
            weaponName = baseWithoutMp + "_tactical_mp";
        }
        
        self.tempBindFunction = ::selectWeapon;
        self.tempBindName = self.selectedPistolName + 
            ((attachmentType == "none") ? "" : " with " + self.LD1[self.currentMenu]["opt"][self.menuCurs]);
        self.tempBindInput = weaponName;
        
        // Return to bind setup menu
        self.isSelectingFunction = undefined;
        self startBindProcess();
        return;
    }
    
    // If we're in a weapon selection menu (not a submenu)
    if(isDefined(self.LD1[self.currentMenu]["input"][self.menuCurs]) && 
       isSubStr(self.LD1[self.currentMenu]["input"][self.menuCurs], "_mp"))
    {
        // Store weapon data for binding
        self.tempBindFunction = ::selectWeapon;
        self.tempBindName = self.LD1[self.currentMenu]["opt"][self.menuCurs];
        self.tempBindInput = self.LD1[self.currentMenu]["input"][self.menuCurs];
        
        // Return to bind setup menu
        self.isSelectingFunction = undefined;
        self startBindProcess();
        return;
    }

    // For non-weapon functions that can be bound directly
    if(currentFunc != ::openMoreMenu && 
       currentFunc != ::openHostMenu && 
       currentFunc != ::returnToParentMenu &&
       currentFunc != ::openWeaponsMenu &&
       currentFunc != ::openSniperMenu &&
       currentFunc != ::openPistolMenu &&
       currentFunc != ::openSMGMenu &&
       currentFunc != ::openGameSettings &&
       currentFunc != ::openTrickshotMenu &&  // Add these two exceptions
       currentFunc != ::openKillstreakMenu)   // for submenu navigation
    {
        // Store function data for binding
        self.tempBindFunction = currentFunc;
        self.tempBindName = self.LD1[self.currentMenu]["opt"][self.menuCurs];
        if(isDefined(self.LD1[self.currentMenu]["input"][self.menuCurs]))
            self.tempBindInput = self.LD1[self.currentMenu]["input"][self.menuCurs];
            
        // Return to bind setup menu
        self.isSelectingFunction = undefined;
        self startBindProcess();
        return;
    }

    // For all other cases (submenus), treat as menu navigation
    self thread [[currentFunc]](self.LD1[self.currentMenu]["input"][self.menuCurs]);
}

openStanceMenu()
{
    destroyHUD();
    
    self.LD1["bind_stance"] = [];
    self.LD1["bind_stance"]["title"] = "Select Stance";
    self.LD1["bind_stance"]["opt"] = [
        "Standing",
        "Crouching",
        "Prone",
        "Back"
    ];
    
    self.LD1["bind_stance"]["function"] = [
        ::selectStance,
        ::selectStance,
        ::selectStance,
        ::returnToBindSetup
    ];
    
    self.LD1["bind_stance"]["input"] = ["stand", "crouch", "prone", ""];
    self.LD1["bind_stance"]["parent"] = "bind_setup";
    
    self.currentMenu = "bind_stance";
    self.menuCurs = 0;
    createHUD(self.LD1[self.currentMenu]["opt"]);
}

openDpadMenu()
{
    destroyHUD();
    
    self.LD1["bind_dpad"] = [];
    self.LD1["bind_dpad"]["title"] = "Select D-Pad";
    self.LD1["bind_dpad"]["opt"] = [
        "D-Pad Up",
        "D-Pad Down",
        "D-Pad Left",
        "D-Pad Right",
        "Back"
    ];
    
    self.LD1["bind_dpad"]["function"] = [
        ::selectDpad,
        ::selectDpad,
        ::selectDpad,
        ::selectDpad,
        ::returnToBindSetup
    ];
    
    self.LD1["bind_dpad"]["input"] = ["dpadup", "dpaddown", "dpadleft", "dpadright", ""];
    self.LD1["bind_dpad"]["parent"] = "bind_setup";
    
    self.currentMenu = "bind_dpad";
    self.menuCurs = 0;
    createHUD(self.LD1[self.currentMenu]["opt"]);
}

selectStance(stance)
{
    self.tempBindStance = stance;
    self returnToBindSetup();
}

selectDpad(dpad)
{
    self.tempBindDpad = dpad;
    self returnToBindSetup();
}

returnToBindSetup()
{
    self openBindSetupMenu();
}

confirmBindControls()
{
    if(!isDefined(self.tempBindStance) || !isDefined(self.tempBindDpad))
    {
        self iPrintln("Please set both Stance and D-Pad controls");
        return;
    }
    
    combo = self.tempBindStance + "_" + self.tempBindDpad;
    self finalizeBind(combo);
}

monitorMenuInput()
{
    self endon("disconnect");
    
    for(;;)
    {
        if(self UseButtonPressed())
        {
            if(isDefined(self.isSelectingFunction) && self.isSelectingFunction)
            {
                self selectBindFunction();
            }
            else
            {
                // Normal menu handling
                self thread [[self.LD1[self.currentMenu]["function"][self.menuCurs]]](self.LD1[self.currentMenu]["input"][self.menuCurs]);
            }
            wait 0.18;
        }
        wait 0.05;
    }
}


finalizeBind(combo)
{
    // Validate all required data is present
    if(!isDefined(self.tempBindFunction) || !isDefined(self.tempBindStance) || !isDefined(self.tempBindDpad))
        return;

    // Find first available bind slot
    bindIndex = -1;
    for(i = 0; i < 2; i++)
    {
        if(!isDefined(self.pers["binds"][i]))
        {
            bindIndex = i;
            break;
        }
    }

    // If no slots available, return
    if(bindIndex == -1)
    {
        self iPrintln("No available bind slots!");
        return;
    }

    // Store in persistent data
    self.pers["binds"][bindIndex] = [];
    self.pers["binds"][bindIndex]["combo"] = combo;
    self.pers["binds"][bindIndex]["function"] = self.tempBindFunction;
    self.pers["binds"][bindIndex]["name"] = self.tempBindName;
    if(isDefined(self.tempBindInput))
        self.pers["binds"][bindIndex]["input"] = self.tempBindInput;

    // Update temporary storage to match
    self.binds = self.pers["binds"];
    
    // Clear temporary variables
    self.tempBindFunction = undefined;
    self.tempBindName = undefined;
    self.tempBindStance = undefined;
    self.tempBindDpad = undefined;
    self.tempBindInput = undefined;
    
    // Update menu and notify player
    self updateBindMenuOptions();
    self iPrintln("Bind created: " + combo);
    
    // Return to bind manager menu
    self openBindMenu();
}

openBindSetupMenu()
{
    self startBindProcess();
}

saveBind()
{
    if(!isDefined(self.tempBindFunction) || !isDefined(self.tempBindStance) || !isDefined(self.tempBindDpad))
    {
        self iPrintln("Please set Function, Stance and D-Pad controls");
        return;
    }
    
    combo = self.tempBindStance + "_" + self.tempBindDpad;
    self finalizeBind(combo);
}

cancelBind()
{
    // Clear temporary variables
    self.tempBindFunction = undefined;
    self.tempBindName = undefined;
    self.tempBindStance = undefined;
    self.tempBindDpad = undefined;
    
    // Return to bind manager menu
    self openBindMenu();
}

// Add this function to handle bound weapon functions
executeBoundWeaponFunction(bindIndex)
{
    if(!isDefined(self.binds[bindIndex]))
        return;
        
    bind = self.binds[bindIndex];
    
    // Initialize weapons menu data if it doesn't exist
    if(!isDefined(self.LD1["weapons"]))
    {
        self.LD1["weapons"] = [];
        self.LD1["weapons"]["opt"] = [];
        self.LD1["weapons"]["input"] = [];
    }
    
    // Set up the menu state to match the weapon selection
    self.currentMenu = "weapons";
    self.menuCurs = 0;
    
    // Set the weapon data in the menu structure
    self.LD1["weapons"]["opt"][0] = bind["name"];
    self.LD1["weapons"]["input"][0] = bind["input"];
    
    // Now call selectWeapon which will use these values
    self selectWeapon();
}

updateToggleText(menuName, optionIndex, newText)
{
    // Update the menu option text
    if(isDefined(self.LD1[menuName]))
    {
        self.LD1[menuName]["opt"][optionIndex] = newText;
        
        // Only update the HUD text if we're currently in that menu
        if(self.currentMenu == menuName && isDefined(self.menuHUD[optionIndex]))
        {
            self.menuHUD[optionIndex]["text"] setText(newText);
        }
    }
}

toggleFastWeaponSwitch()
{
    if(!isDefined(self.pers["instashoot"]))
        self.pers["instashoot"] = 0;
    
    self.pers["instashoot"] = !self.pers["instashoot"];
    
    // Update menu text only in trickshot menu
    newText = "Instashoot " + (self.pers["instashoot"] ? "^2[ON]" : "^1[OFF]");
    self updateToggleText("trickshot", 0, newText);
    
    if(self.pers["instashoot"])
    {
        self iPrintln("^2Insta Switch: ON");
        self thread doSngliS();
    }
    else
    {
        self iPrintln("^1Insta Switch: OFF");
        self notify("stop_sngliS");
    }
}

toggleDistanceFeed()
{
    if(!isDefined(self.pers["showDistanceFeed"]))
        self.pers["showDistanceFeed"] = 0;
    
    self.pers["showDistanceFeed"] = !self.pers["showDistanceFeed"];
    
    // Update menu text only in game_settings menu
    newText = "Toggle Distance Feed " + (self.pers["showDistanceFeed"] ? "^2[ON]" : "^1[OFF]");
    self updateToggleText("game_settings", 0, newText);
    
    self iPrintln("Distance Feed: " + (self.pers["showDistanceFeed"] ? "^2ON" : "^1OFF"));
}

toggleTeamAmmo()
{
    self endon("disconnect");

    // Initialize in persistent data if not defined
    if(!isDefined(self.pers["teamAmmoActive"]))
        self.pers["teamAmmoActive"] = 1;

    self.pers["teamAmmoActive"] = !self.pers["teamAmmoActive"];
    
    // Update menu text only in game_settings menu
    newText = "Toggle Infinite Reserve" + (self.pers["teamAmmoActive"] ? " ^2[ON]" : " ^1[OFF]");
    self updateToggleText("game_settings", 1, newText);

    if(self.pers["teamAmmoActive"])
    {
        self iPrintln("^2Infinite Reserve: ON");
        self thread monitorTeamAmmo();
    }
    else
    {
        self iPrintln("^2Infinite Reserve: OFF");
        self notify("stop_team_ammo");
    }
}

toggleCanswap()
{
    if(!isDefined(self.pers["canswap"]))
        self.pers["canswap"] = 0;
    
    self.pers["canswap"] = !self.pers["canswap"];
    
    // Update menu text with new wording
    newText = "Always Canswap " + (self.pers["canswap"] ? "^2[ON]" : "^1[OFF]");
    self updateToggleText("trickshot", 1, newText);
    
    if(self.pers["canswap"])
    {
        self iPrintln("^2Always Canswap: ON");
        self thread monitorCanswapPersistent();
    }
    else
    {
        self iPrintln("^1Always Canswap: OFF");
        self notify("stop_canswap");
    }
}

monitorCanswapPersistent()
{
    self endon("disconnect");
    self endon("stop_canswap");
    
    for(;;)
    {
        // Wait for spawn if dead
        if(!isAlive(self))
            self waittill("spawned");
            
        // Start monitoring this life
        self thread monitorCanswapLife();
        
        // Wait for death to restart monitoring
        self waittill("death");
    }
}

monitorCanswapLife()
{
    self endon("death");
    self endon("disconnect");
    self endon("stop_canswap");
    
    for(;;)
    {
        self waittill("weapon_change");
        
        currentWeapon = self getCurrentWeapon();
        if(currentWeapon != "none" && !isSubStr(currentWeapon, "grenade"))
        {
            self takeWeapon(currentWeapon);
            self giveWeapon(currentWeapon);
            self switchToWeapon(currentWeapon);
        }
        
        wait 0.05;
    }
}
// Update other toggle functions similarly...

doPredMala()
{
    currentWeapon = self getCurrentWeapon();
    if(currentWeapon == "none")
        return;
        
    self takeWeapon(currentWeapon);
    self GiveWeapon("killstreak_predator_missile_mp");
    self switchToWeapon("killstreak_predator_missile_mp");
    wait 0.1;
    self giveWeapon(currentWeapon, 0);
    
    self iPrintln("PredMala Executed");
}

doCowboy()
{
    self GiveWeapon("aa12_eotech_fmj_mp");
    self switchToWeapon("aa12_eotech_fmj_mp");
    
    // Wait until we're actually holding the AA12
    while(self getCurrentWeapon() != "aa12_eotech_fmj_mp")
        wait 0.05;
        
    self setClientDvar("perk_weapreloadmultiplier", "0.0001");
    self iPrintln("Cowboy: ^2ON");
    
    self thread monitorCowboyWeapon();
}

monitorCowboyWeapon()
{
    self endon("death");
    self endon("disconnect");
    
    oldWeapon = self getCurrentWeapon();
    
    for(;;)
    {
        currentWeapon = self getCurrentWeapon();
        if(currentWeapon != oldWeapon && oldWeapon == "aa12_eotech_fmj_mp")
        {
            // Only clean up if we actually switched from the AA12
            self setClientDvar("perk_weapReloadMultiplier", "0.5");
            self takeWeapon("aa12_eotech_fmj_mp");
            self iPrintln("Cowboy: ^1OFF");
            break;
        }
        oldWeapon = currentWeapon;
        wait 0.05;
    }
}

// Add this new function to open the switch teams menu
openSwitchTeamsMenu()
{
    if(!self isHost())
        return;
    
    // Create the switch teams menu if it doesn't exist
    if(!isDefined(self.LD1["switch_teams"]))
    {
        self.LD1["switch_teams"] = [];
        self.LD1["switch_teams"]["title"] = "Switch Teams Menu";
        self.LD1["switch_teams"]["opt"] = [];
        self.LD1["switch_teams"]["function"] = [];
        self.LD1["switch_teams"]["input"] = [];
        self.LD1["switch_teams"]["parent"] = "host";
    }
    
    // Get host team
    hostTeam = getHostTeam();
    enemyTeam = (hostTeam == "allies") ? "axis" : "allies";
    
    // Build the menu with enemy players
    self.LD1["switch_teams"]["opt"] = [];
    self.LD1["switch_teams"]["function"] = [];
    self.LD1["switch_teams"]["input"] = [];
    
    playerCount = 0;
    
    // Add enemy players to the menu
    foreach(player in level.players)
    {
        if(isDefined(player.pers["team"]) && player.pers["team"] == enemyTeam)
        {
            self.LD1["switch_teams"]["opt"][playerCount] = player.name;
            self.LD1["switch_teams"]["function"][playerCount] = ::switchPlayerTeam;
            self.LD1["switch_teams"]["input"][playerCount] = player getEntityNumber();
            playerCount++;
        }
    }
    
    // Add back option
    self.LD1["switch_teams"]["opt"][playerCount] = "Back";
    self.LD1["switch_teams"]["function"][playerCount] = ::returnToParentMenu;
    self.LD1["switch_teams"]["input"][playerCount] = "";
    
    // Open the menu
    destroyHUD();
    self.currentMenu = "switch_teams";
    self.menuCurs = isDefined(self.menuLastCurs[self.currentMenu]) ? self.menuLastCurs[self.currentMenu] : 0;
    createHUD(self.LD1[self.currentMenu]["opt"]);
}

// Function to switch a player's team
switchPlayerTeam()
{
    if(!self isHost())
        return;
    
    // Get the player entity number from the menu input
    playerNum = self.LD1[self.currentMenu]["input"][self.menuCurs];
    targetPlayer = getPlayerByNum(playerNum);
    
    if(!isDefined(targetPlayer))
    {
        self iPrintln("^1Error: Player not found");
        return;
    }
    
    // Get host team
    hostTeam = self.pers["team"];
    
    // Only switch if player is not already on host team
    if(targetPlayer.pers["team"] != hostTeam)
    {
        // Force player to host team
        forcePlayerToTeam(targetPlayer, hostTeam);
        
        // Notify everyone
        foreach(player in level.players)
            player iPrintln(targetPlayer.name + " ^7switched to " + (hostTeam == "allies" ? "^5Allies" : "^1Axis"));
        
        // Refresh the menu
        self openSwitchTeamsMenu();
    }
}

// Function to force a player to a specific team
forcePlayerToTeam(player, team)
{
    if(!level.teamBased)
        return;
    
    
    // Set up proper team switching flags
    if(player.sessionstate == "playing")
    {
        player.switching_teams = true;
        player.joining_team = team;
        player.leaving_team = player.pers["team"];
        player suicide();
    }
    
    // Set team properties
    player.sessionteam = team;
    player.team = team;
    
    // Use game's function to add to team
    player maps\mp\gametypes\_menus::addToTeam(team);
    player.pers["class"] = undefined;
    player.class = undefined;
    
    // Notify events
    player notify("joined_team");
    level notify("joined_team");
    
    // Trigger respawn
    player notify("end_respawn");
    
    // Begin class choice
    player thread maps\mp\gametypes\_menus::beginClassChoice();
}

// Helper function to get player by number (renamed to avoid conflict)
getPlayerByNum(num)
{
    foreach(player in level.players)
    {
        if(player getEntityNumber() == num)
            return player;
    }
    return undefined;
}

setOneBullet()
{
    currentWeapon = self getCurrentWeapon();
    
    if(currentWeapon != "none")
    {
        self setWeaponAmmoClip(currentWeapon, 1);
        self iPrintln("^2Set to One Bullet");
    }
    else
    {
        self iPrintln("^1No weapon equipped");
    }
}
