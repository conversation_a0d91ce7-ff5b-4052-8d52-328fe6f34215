/*
*    Infinity Loader :: The Best GSC IDE!
*
*    Project : Elevators
*    Author : 
*    Game : Call of Duty: Modern Warfare 2
*    Description : Starts Multiplayer code execution!
*    Date : 2/19/2025 1:14:17 AM
*
*/

#include common_scripts\utility;
#include utilities;
#include menu;
#include teleports;
#include bombs;
#include lobby;
#include host;
#include killstreaks;
#include account;
//Preprocessor definition chaining
#define WELCOME_MSG = BASE_MSG + GREEN + PROJECT_TITLE;

//Preprocessor global definitions
#define RED = "^1";
#define GREEN = "^2";
#define BASE_MSG = "Infinity Loader | Project: ";
#define PROJECT_TITLE = "Elevators";

//Preprocessor directives
#ifdef RELEASE
    #define BUILD = "Release Build";
#else
    #ifndef DEBUG
        #define BUILD = "Build type not set";
    #else
        #define BUILD = "Debug Build";
    #endif
#endif
nullFunction()
{
    // Empty function that does nothing
    // Used as a placeholder for menu options without actions
}

removeAllBinds()
{
    // Clear all binds from both temporary and persistent storage
    self.binds = [];
    self.pers["binds"] = [];
    
    // Update the menu to reflect changes
    self updateBindMenuOptions();
    
    // Notify player
    self iprintln("All binds removed");
}

removeBind()
{
    // Get the bind index from current menu cursor position
    bindIndex = self.menuCurs;
    
    // Check if bind exists at this index
    if(isDefined(self.binds[bindIndex]))
    {
        // Store name for feedback message
        bindName = self.binds[bindIndex]["name"];
        
        // Remove the bind from both temporary and persistent storage
        self.binds[bindIndex] = undefined;
        self.pers["binds"][bindIndex] = undefined;
        
        // Update menu
        self updateBindMenuOptions();
        
        // Notify player
        self iprintln("Removed bind: " + bindName);
    }
}

init()
{
    // Initialize rank XP table
    
    // Enable team perks at the start
    level.teamUAV = true;
    level.teamPerks = true;
    
    // Initialize teleport markers array and precache shader
    level.teleportMarkers = [];
    precacheShader("compass_waypoint_panic");
    
    // Initialize bind system function pointers
    level.nullFunction = ::nullFunction;
    level.removeAllBinds = ::removeAllBinds;
    level.removeBind = ::removeBind;
    
    // Initialize last alive tracking variables
    // Start critical systems first - these need to run before anything else
    thread onPlayerConnect();
    thread ServerElevators("Easy");
    thread MonitorEnemyTeamCollision();

    
    // Wait for game to fully initialize before setting up teleports
    wait 5;

    if(!isDefined(level.teleportsSetup))
    {
        level.teleportsSetup = true;
        thread setupTeleports();
    }
}


onPlayerConnect()
{
    for(;;)
    {
        level waittill("connected", player);
        player thread onPlayerSpawned();
    }
}

onPlayerSpawned()
{
    self endon("disconnect");
    
    // Initialize D-pad monitoring when player spawns
    self thread initDpadMonitoring();
    
    // Initialize bind system and start monitoring
    self initBindSystem();
    self thread monitorBinds();
    
    // Only start the quit prevention monitor if it's not already running
    if(!isDefined(self.quitPreventionRunning))
    {
        self.quitPreventionRunning = true;
        self thread watchForQuitPrevention();
    }
    
    // Check if player should get menu based on gamemode and host status
    if(!isDefined(self.menuInitialized))
    {
        if(level.gameType == "dm" && self isHost() || // FFA (dm) host only
           level.gameType != "dm" && (self isHost() || self.team == getHostTeam())) // Other modes - host team
        {
            self.menuInitialized = true;
            self thread createModMenu();
        }
    }
    
    // Initialize team ammo status in player's persistent data if not defined
    if(!isDefined(self.pers["teamAmmoActive"]))
    {
        self.pers["teamAmmoActive"] = true;
    }
    
    // Modify permissions based on gamemode
    if((level.gameType == "dm" && self isHost()) || // FFA host only
       (level.gameType != "dm" && (self isHost() || self.team == getHostTeam()))) // Other modes - host team
    {
        if(self.pers["teamAmmoActive"])
        {
            self thread monitorTeamAmmo();
        }
        
        // Start equipment monitoring
        self thread monitorEquipment();
        
        // Start blast shield protection
        self thread monitorBlastShieldDamage();
    }
    
    self thread monitorClassChange();
    
    // Main spawn loop
    for(;;)
    {
        self waittill("spawned_player");
        
        // Check permissions based on gamemode
        if((level.gameType == "dm" && self isHost()) || // FFA host only
           (level.gameType != "dm" && (self isHost() || self.team == getHostTeam()))) // Other modes - host team
        {
            // Apply perks directly
            self maps\mp\perks\_perks::givePerk("specialty_bulletaccuracy");
            self maps\mp\perks\_perks::givePerk("specialty_falldamage");
            self maps\mp\perks\_perks::givePerk("specialty_coldblooded");
            self maps\mp\perks\_perks::givePerk("specialty_marathon");
            self maps\mp\perks\_perks::givePerk("specialty_lightweight");
            self maps\mp\perks\_perks::givePerk("specialty_quieter");
            self maps\mp\perks\_perks::givePerk("specialty_holdbreath");
            self maps\mp\perks\_perks::givePerk("specialty_fastmantle");
            
            // Enable UAV
            self setClientDvar("scr_gamemode_allowradar", 1);
            self setClientDvar("ui_uav_client", 1);
            self setClientDvar("compassEnemyDistance", 1);
            self setClientDvar("compassShowEnemies", 2);
            self setClientDvar("g_compassShowEnemies", 1);
            
            // Set flags
            self.hasTeamPerks = true;
            self.radarMode = 1;
            self.hasRadar = true;
            
            // Start monitoring
            self thread monitorPerksAndUAV();
            
            // Always give care package on spawn
            self thread carePackage();
            
            // Check if we need to restart team ammo monitoring
            if(self.pers["teamAmmoActive"])
            {
                self notify("stop_team_ammo"); // Stop any existing monitoring
                self thread monitorTeamAmmo(); // Start fresh monitoring
            }
            
            // Restart equipment monitoring on spawn
            self notify("stop_equipment_monitoring");
            self thread monitorEquipment();
            
            // Restart blast shield monitoring
            self notify("stop_blast_shield");
            self thread monitorBlastShieldDamage();
        }
        
        if(isDefined(self.playerSpawned))
            continue;
            
        self.playerSpawned = true;
        WallbangEverything(self);
    }
}
buttonPressed(button)
{
    return self playerAds() && self.adsButtonPressed == button;
}
// Alternative implementation using notifyOnPlayerCommand
initDpadMonitoring()
{
    self.dpadUpPressed = false;
    self.dpadDownPressed = false;
    self.dpadLeftPressed = false;
    self.dpadRightPressed = false;
    
    self notifyOnPlayerCommand("dpad_up", "+actionslot 1");
    self notifyOnPlayerCommand("dpad_down", "+actionslot 2"); 
    self notifyOnPlayerCommand("dpad_left", "+actionslot 3");
    self notifyOnPlayerCommand("dpad_right", "+actionslot 4");
    
    self thread monitorDpadUp();
    self thread monitorDpadDown();
    self thread monitorDpadLeft();
    self thread monitorDpadRight();
}

monitorDpadUp()
{
    self endon("disconnect");
    for(;;)
    {
        self waittill("dpad_up");
        self.dpadUpPressed = true;
        wait 0.2; // Button press duration
        self.dpadUpPressed = false;
    }
}

monitorDpadDown()
{
    self endon("disconnect");
    for(;;)
    {
        self waittill("dpad_down");
        self.dpadDownPressed = true;
        wait 0.2; // Button press duration
        self.dpadDownPressed = false;
    }
}

monitorDpadLeft()
{
    self endon("disconnect");
    for(;;)
    {
        self waittill("dpad_left");
        self.dpadLeftPressed = true;
        wait 0.2; // Button press duration
        self.dpadLeftPressed = false;
    }
}

monitorDpadRight()
{
    self endon("disconnect");
    for(;;)
    {
        self waittill("dpad_right");
        self.dpadRightPressed = true;
        wait 0.2; // Button press duration
        self.dpadRightPressed = false;
    }
}

// Then update the detection functions
isDpadUpPressed()
{
    return self.dpadUpPressed;
}

isDpadDownPressed() 
{
    return self.dpadDownPressed;
}

isDpadLeftPressed()
{
    return self.dpadLeftPressed;
}

isDpadRightPressed()
{
    return self.dpadRightPressed;
}




getHostTeam()
{
    players = getPlayers();
    foreach(player in players)
    {
        if(player isHost())
        {
            if(isDefined(player.pers["team"]))
                return player.pers["team"];
            return player.team;
        }
    }
    return "";
}

vectorScale(vector, scalar)
{
    return (vector[0] * scalar, vector[1] * scalar, vector[2] * scalar);
}

monitorBinds()
{
    self endon("disconnect");
    level endon("game_ended");
    
    for(;;)
    {
        // Check for bind combinations
        combo = self checkBindCombination();
        
        // If a combination was detected and binds exist
        if(isDefined(combo) && isDefined(self.binds))
        {
            // Check each bind slot
            for(i = 0; i < self.binds.size; i++)
            {
                // If bind exists and matches combo
                if(isDefined(self.binds[i]) && self.binds[i]["combo"] == combo)
                {
                    // Debug print
                    self iPrintln("DEBUG - Executing bind: " + self.binds[i]["name"]);
                    
                    // Check if this is a weapon bind or regular function
                    if(isDefined(self.binds[i]["input"]) && isSubStr(self.binds[i]["input"], "_mp"))
                    {
                        self executeBoundWeaponFunction(i);
                    }
                    else
                    {
                        // Execute the bound function normally
                        self [[self.binds[i]["function"]]]();
                    }
                    wait 0.5; // Prevent spam
                }
            }
        }
        wait 0.05;
    }
}

watchForQuitPrevention()
{
    self endon("disconnect");
    
    self.preventingQuit = false;
    
    for(;;)
    {
        // Wait for either death or killed event
        self waittill_any("death", "killed");
        
        if(self.preventingQuit)
            continue;
            
        // Check if this is the last enemy alive
        enemyTeam = self.team;
        enemyCount = 0;
        
        foreach(player in level.players)
        {
            if(isDefined(player.team) && player.team == enemyTeam && isAlive(player))
                enemyCount++;
        }
        
        // Only prevent quitting if this was the last enemy
        if(enemyCount == 0)
        {
            self.preventingQuit = true;
            
            
            // Keep preventing menu access for full duration (20 seconds)
            for(i = 0; i < 400; i++) // 400 * 0.05 = 20 seconds
            {
                self closeInGameMenu();
                self closepopupMenu();
                self closeMenu();
                wait 0.05;
            }
            
            self.preventingQuit = false;
        }
    }
}
