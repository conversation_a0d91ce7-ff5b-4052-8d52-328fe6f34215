<meta charset="utf-8">
<link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css">
<body>

<style>
body
{
background-color: #191919 !important;
font-size: 18px;
color: #cccccc;
}

.block-style
{
background-color: #303030;
border-color: #bce8f1;
padding-left: 15px;
border: 1px solid;
border-radius: 4px
}

.text-live
{
color: #8492a6;
}

.content
{
padding: 0px 40px 40px 40px;
}

.scrollbar
{
height: 100%;
width: 100%;
overflow-y: auto;
overflow-x: hidden;
}

#style-1::-webkit-scrollbar
{
width: 12px;
background-color: #191919;
}

#style-1::-webkit-scrollbar-thumb
{
border-radius: 2px;
-webkit-box-shadow: inset 0 0 6px rgba(0,0,0,.3);
box-shadow: inset 0 0 6px rgba(0,0,0,.3);
background-color: #555;
}

#style-1::-webkit-scrollbar-track
{
-webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.3);
box-shadow: inset 0 0 6px rgba(0,0,0,0.3);
border-radius: 2px;
background-color: #303030;
}
</style>

<div class="scrollbar" id="style-1">
<div class="content">

## :sparkles: TODO :sparkles:

:::{.alert .block-style .text-muted}
- what needs to be done?
:::

## :fire: Changelog :fire:

<!-- current version -->
:::{.alert .block-style .text-live}
Version - Lollipop :lollipop:
- Added Ferris Wheel :ferris_wheel:
- Added Infinite Roller Coaster :roller_coaster:
:::

<!-- version ******* -->
:::{.alert .block-style .text-live}
Version - Doughnut :doughnut:
- whats new?
:::

<!-- version ******* -->
:::{.alert .block-style .text-live}
Version - Icecream :icecream:
- whats new?
:::

![Infinity Loader](https://www.youtube.com/watch?v=OgtJ8_wQXi0)

[MarkDown Cheat Sheet](https://www.digitaltapestry.net/posts/markdig-cheat-sheet)

</div>
</div>
</body>